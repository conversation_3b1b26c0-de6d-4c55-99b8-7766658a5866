import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import {
  BusinessUpdateProductDto,
  BusinessProductResponseDto as ProductResponseDto,
} from '../../../dto';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { ProductTypeEnum } from '@modules/business/enums';
import { UserProduct } from '@modules/business/entities';
import { UpdateProductProcessor } from './update-product.processor';
import { PhysicalProductUpdateProcessor } from './physical-product-update.processor';
import { DigitalProductUpdateProcessor } from './digital-product-update.processor';
import { EventProductUpdateProcessor } from './event-product-update.processor';
import { ServiceProductUpdateProcessor } from './service-product-update.processor';
import { ComboProductUpdateProcessor } from './combo-product-update.processor';
import { InventoryResponseDto } from '../../../dto/inventory';
import { ClassificationResponseDto } from '../../../dto';

/**
 * Orchestrator chính cho việc cập nhật sản phẩm
 * Điều phối toàn bộ luồng update từ các processor theo loại sản phẩm
 */
@Injectable()
export class UpdateProductOrchestrator {
  private readonly logger = new Logger(UpdateProductOrchestrator.name);

  constructor(
    private readonly updateProcessor: UpdateProductProcessor,
    private readonly physicalProductUpdateProcessor: PhysicalProductUpdateProcessor,
    private readonly digitalProductUpdateProcessor: DigitalProductUpdateProcessor,
    private readonly eventProductUpdateProcessor: EventProductUpdateProcessor,
    private readonly serviceProductUpdateProcessor: ServiceProductUpdateProcessor,
    private readonly comboProductUpdateProcessor: ComboProductUpdateProcessor,
  ) {}

  /**
   * Method chính để cập nhật sản phẩm
   * Sử dụng processors chuyên biệt theo loại sản phẩm
   */
  @Transactional()
  async updateProduct(
    id: number,
    updateProductDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<ProductResponseDto> {
    try {
      this.logger.log(`Bắt đầu cập nhật sản phẩm ID: ${id} cho user: ${userId}`);

      // BƯỚC 1: Tìm và validate sản phẩm
      const product = await this.updateProcessor.findAndValidateProduct(id, userId);

      // BƯỚC 2: Cập nhật thông tin cơ bản (chung cho tất cả loại sản phẩm)
      this.updateProcessor.updateBasicFields(product, updateProductDto);

      // BƯỚC 3: Cập nhật giá sản phẩm (chung cho tất cả loại sản phẩm)
      this.updateProcessor.updateProductPricing(product, updateProductDto);

      // BƯỚC 4: Xử lý hình ảnh sản phẩm chính
      const imagesUploadUrls = await this.processMainProductImages(updateProductDto);

      // BƯỚC 5: Cập nhật custom fields và metadata (chung)
      await this.updateProcessor.updateCustomFields(product, updateProductDto);

      // BƯỚC 6: Xử lý cập nhật theo loại sản phẩm cụ thể
      const productSpecificResult = await this.processProductTypeSpecificUpdate(
        product,
        updateProductDto,
        userId
      );

      // BƯỚC 7: Xử lý classifications (chung cho tất cả loại sản phẩm)
      const { classifications, classificationUploadUrls } = await this.updateProcessor.processClassificationsUpdate(
        id,
        updateProductDto,
        userId
      );

      // BƯỚC 8: Xử lý xóa classifications
      await this.updateProcessor.processClassificationsDeletion(
        id,
        updateProductDto.classificationsToDelete || [],
        userId
      );

      // BƯỚC 9: Xây dựng response cuối cùng
      const response = await this.updateProcessor.buildUpdateResponse(
        productSpecificResult.product,
        imagesUploadUrls,
        productSpecificResult.advancedImagesUploadUrls,
        classificationUploadUrls,
        classifications,
        productSpecificResult.inventory
      );

      this.logger.log(`Hoàn thành cập nhật sản phẩm ID: ${id} (${product.productType})`);
      return response;

    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật sản phẩm ID: ${id}`, error.stack);

      // Nếu là AppException, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Wrap lỗi khác thành AppException
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_UPDATE_FAILED,
        `Lỗi khi cập nhật sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Xử lý cập nhật theo loại sản phẩm cụ thể
   */
  private async processProductTypeSpecificUpdate(
    product: UserProduct,
    updateProductDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<UpdateProductResult> {
    this.logger.log(`Processing ${product.productType} specific update for product ${product.id}`);

    switch (product.productType) {
      case ProductTypeEnum.PHYSICAL:
        return await this.physicalProductUpdateProcessor.updatePhysicalProduct(
          product,
          updateProductDto,
          userId
        );

      case ProductTypeEnum.DIGITAL:
        return await this.digitalProductUpdateProcessor.updateDigitalProduct(
          product,
          updateProductDto,
          userId
        );

      case ProductTypeEnum.EVENT:
        return await this.eventProductUpdateProcessor.updateEventProduct(
          product,
          updateProductDto,
          userId
        );

      case ProductTypeEnum.SERVICE:
        return await this.serviceProductUpdateProcessor.updateServiceProduct(
          product,
          updateProductDto,
          userId
        );

      case ProductTypeEnum.COMBO:
        return await this.comboProductUpdateProcessor.updateComboProduct(
          product,
          updateProductDto,
          userId
        );

      default:
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          `Loại sản phẩm không được hỗ trợ: ${product.productType}`,
        );
    }
  }

  /**
   * Xử lý hình ảnh sản phẩm chính
   */
  private async processMainProductImages(updateProductDto: BusinessUpdateProductDto): Promise<any[]> {
    const imagesUploadUrls: any[] = [];
    const now = Date.now();

    // TODO: Implement logic xử lý hình ảnh từ service gốc
    // Xử lý hình ảnh theo 3 cách: imagesMediaTypes, imageOperations, hoặc images (deprecated)
    // await this.processImageUpdates(updateProductDto, product, imagesUploadUrls, now);

    return imagesUploadUrls;
  }

  /**
   * Xử lý image operations cho advanced info khi không có advancedInfo trong request
   */
  private async processAdvancedImageOperations(
    product: any,
    updateProductDto: BusinessUpdateProductDto
  ): Promise<void> {
    // Xử lý imageOperations cho ticket types/service packages
    if (this.shouldProcessAdvancedImageOperations(product, updateProductDto)) {
      try {
        this.logger.log(`Xử lý imageOperations cho ${product.productType} product với ${updateProductDto.imageOperations?.length || 0} operations`);
        
        // TODO: Implement logic từ service gốc
        // await this.processAdvancedImageOperations(...)
        
      } catch (imageOperationsError) {
        this.logger.warn(`Không thể xử lý imageOperations cho sản phẩm ${product.id}: ${imageOperationsError.message}`);
      }
    }

    // Xử lý images (deprecated) cho ticket types/service packages
    if (this.shouldProcessAdvancedImages(product, updateProductDto)) {
      try {
        this.logger.log(`Xử lý images cho ${product.productType} advanced info với ${updateProductDto.images?.length || 0} operations`);
        
        // TODO: Implement logic từ service gốc
        // await this.processAdvancedImages(...)
        
      } catch (imagesError) {
        this.logger.warn(`Không thể xử lý images cho advanced info của sản phẩm ${product.id}: ${imagesError.message}`);
      }
    }
  }

  /**
   * Kiểm tra có nên xử lý advanced image operations không
   */
  private shouldProcessAdvancedImageOperations(product: any, updateProductDto: BusinessUpdateProductDto): boolean {
    return !updateProductDto.advancedInfo &&
           updateProductDto.imageOperations &&
           updateProductDto.imageOperations.length > 0 &&
           product.detail_id &&
           (product.productType === 'EVENT' || product.productType === 'SERVICE');
  }

  /**
   * Kiểm tra có nên xử lý advanced images không
   */
  private shouldProcessAdvancedImages(product: any, updateProductDto: BusinessUpdateProductDto): boolean {
    const isAdvancedImages = !(updateProductDto as any).isProductImageOperations;
    
    return !updateProductDto.advancedInfo &&
           updateProductDto.images &&
           updateProductDto.images.length > 0 &&
           product.detail_id &&
           (product.productType === 'EVENT' || product.productType === 'SERVICE') &&
           isAdvancedImages;
  }
}

/**
 * Interface cho kết quả xử lý update từ processors chuyên biệt
 */
export interface UpdateProductResult {
  product: UserProduct;
  imagesUploadUrls: string[];
  advancedImagesUploadUrls: string[];
  classificationUploadUrls: string[];
  classifications: ClassificationResponseDto[];
  inventory: InventoryResponseDto[] | null;
}

/**
 * Interface cho context update
 */
export interface UpdateProductContext {
  productId: number;
  userId: number;
  updateDto: BusinessUpdateProductDto;
  timestamp: number;
}

/**
 * Enum cho các bước update
 */
export enum UpdateStep {
  VALIDATE_PRODUCT = 'validate_product',
  UPDATE_BASIC_FIELDS = 'update_basic_fields',
  UPDATE_PRICING = 'update_pricing',
  PROCESS_IMAGES = 'process_images',
  UPDATE_CUSTOM_FIELDS = 'update_custom_fields',
  SAVE_PRODUCT = 'save_product',
  PROCESS_ADVANCED_INFO = 'process_advanced_info',
  PROCESS_CLASSIFICATIONS = 'process_classifications',
  PROCESS_INVENTORY = 'process_inventory',
  BUILD_RESPONSE = 'build_response',
}

/**
 * Interface cho tracking progress
 */
export interface UpdateProgress {
  currentStep: UpdateStep;
  completedSteps: UpdateStep[];
  totalSteps: number;
  errors: string[];
  warnings: string[];
}
