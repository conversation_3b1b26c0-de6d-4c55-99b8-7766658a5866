import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { TypeCustomField } from './request/type-custom-field/type-custom-field.type';

/**
 * DTO cho giá trị custom field trong metadata
 */
export class CustomFieldValueDto {
  @ApiProperty({
    description: 'Giá trị của trường tùy chỉnh',
    example: 'XL',
  })
  @IsNotEmpty()
  value: number | boolean | string | unknown | Date;
}

/**
 * DTO cho custom field trong metadata (copy từ bảng custom_fields + value)
 */
export class MetadataCustomFieldDto {
  @ApiProperty({
    description: 'ID của trường tùy chỉnh',
    example: 5,
  })
  @IsNumber()
  @IsNotEmpty()
  id: number;

  @ApiProperty({
    description: 'ID cấu hình',
    example: 'product_size',
  })
  @IsString()
  @IsNotEmpty()
  configId: string;

  @ApiProperty({
    description: 'Nhãn hiển thị',
    example: 'Kích thước sản phẩm',
  })
  @IsString()
  @IsNotEmpty()
  label: string;

  @ApiProperty({
    description: 'Loại trường',
    example: 'select',
  })
  @IsString()
  @IsNotEmpty()
  type: TypeCustomField;

  @ApiProperty({
    description: 'Cấu hình JSON',
    example: {
      options: ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
      placeholder: 'Chọn kích thước'
    },
  })
  @IsObject()
  @IsNotEmpty()
  configJson: any;

  @ApiProperty({
    description: 'Tags phân loại',
    example: ['product', 'classification'],
  })
  @IsArray()
  @IsString({ each: true })
  tags: string[];

  @ApiProperty({
    description: 'Giá trị của trường tùy chỉnh',
    type: CustomFieldValueDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => CustomFieldValueDto)
  value: CustomFieldValueDto;
}

/**
 * DTO cho custom field input khi tạo/cập nhật
 */
export class CustomFieldInputDto {
  @ApiProperty({
    description: 'ID của trường tùy chỉnh',
    example: 5,
  })
  @IsNumber()
  @IsNotEmpty()
  customFieldId: number;

  @ApiProperty({
    description: 'Giá trị của trường tùy chỉnh',
    type: CustomFieldValueDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => CustomFieldValueDto)
  value: CustomFieldValueDto;
}

/**
 * DTO cho metadata structure
 */
export class MetadataDto {
  @ApiProperty({
    description: 'Danh sách custom fields',
    type: [MetadataCustomFieldDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MetadataCustomFieldDto)
  customFields?: MetadataCustomFieldDto[]; // Đổi từ custom_fields thành customFields để phù hợp với database schema
}
