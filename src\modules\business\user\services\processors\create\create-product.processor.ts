import { Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import {
  UserProductRepository,
  CustomFieldRepository,
} from '@modules/business/repositories';
import { BusinessProductResponseDto as ProductResponseDto } from '../../../dto';
import { EntityStatusEnum, ProductTypeEnum } from '@modules/business/enums';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { UserProduct } from '@modules/business/entities';
import { UserProductHelper } from '../../../helpers/user-product.helper';
import { MetadataHelper } from '../../../helpers/metadata.helper';
import { ValidationHelper } from '../../../helpers/validation.helper';
import { S3Service } from '@shared/services/s3.service';
import { CategoryFolderEnum, generateS3Key } from '@shared/utils/generators/s3-key-generator.util';
import { FileSizeEnum, ImageTypeEnum, TimeIntervalEnum } from '@shared/utils';
import { CreateProductResult } from './create-product-orchestrator';
import { CreatedProductDto } from '../../../dto/request/create-products.dto';

/**
 * Processor chuyên xử lý logic chung cho việc tạo sản phẩm
 * Bóc tách từ UserProductService để tối ưu hóa và dễ maintain
 */
@Injectable()
export class CreateProductProcessor {
  private readonly logger = new Logger(CreateProductProcessor.name);

  constructor(
    private readonly userProductRepository: UserProductRepository,
    private readonly customFieldRepository: CustomFieldRepository,
    private readonly userProductHelper: UserProductHelper,
    private readonly metadataHelper: MetadataHelper,
    private readonly validationHelper: ValidationHelper,
    private readonly s3Service: S3Service,
  ) {}

  /**
   * Validate các trường chung cho tất cả loại sản phẩm
   */
  async validateCommonFields(createDto: CreatedProductDto): Promise<void> {
    // Validate tên sản phẩm
    if (!createDto.name || createDto.name.trim().length === 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Tên sản phẩm không được để trống',
      );
    }

    // Validate loại sản phẩm
    if (!Object.values(ProductTypeEnum).includes(createDto.productType)) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_PRODUCT_TYPE,
        `Loại sản phẩm không hợp lệ: ${createDto.productType}`,
      );
    }

    this.logger.log(`Validated common fields for product: ${createDto.name}`);
  }

  /**
   * Xử lý custom fields và tạo metadata
   */
  async processCustomFields(
    dto: CreatedProductDto,
    additionalMetadata?: any,
  ): Promise<{ customFields: any[]; metadata: any }> {
    let customFields: any[] = [];

    if (dto.customFields && dto.customFields.length > 0) {
      this.logger.log(`Processing ${dto.customFields.length} custom fields for product`);

      // Lấy danh sách ID custom fields
      const customFieldIds = this.metadataHelper.extractCustomFieldIds(dto.customFields);

      // Lấy thông tin chi tiết custom fields từ database
      customFields = await this.customFieldRepository.findByIds(customFieldIds);

      // Validate custom fields
      this.metadataHelper.validateCustomFieldInputs(dto.customFields, customFields);
    }

    // Xử lý metadata
    const metadata = this.metadataHelper.buildMetadata(
      dto.customFields,
      customFields,
      additionalMetadata,
    );

    return { customFields, metadata };
  }

  /**
   * Tạo entity sản phẩm cơ bản
   */
  async createBaseProduct(
    dto: CreatedProductDto,
    userId: number,
    metadata: any,
  ): Promise<UserProduct> {
    const product = new UserProduct();
    
    // Thông tin cơ bản
    product.name = dto.name;
    product.description = dto.description || null;
    product.productType = dto.productType;
    product.tags = dto.tags || [];
    
    // Metadata và custom fields
    product.metadata = metadata;
    
    // Thông tin hệ thống
    product.createdBy = userId;
    product.status = EntityStatusEnum.PENDING;
    product.createdAt = Date.now();
    product.updatedAt = Date.now();
    
    // Embedding fields (sẽ được xử lý sau)
    product.nameEmbedding = null;
    product.descriptionEmbedding = null;
    product.tagsEmbedding = null;
    
    // Images sẽ được xử lý riêng
    product.images = [];
    
    this.logger.log(`Created base product entity: ${dto.name}`);
    return product;
  }

  /**
   * Xử lý hình ảnh sản phẩm và tạo upload URLs
   */
  async processProductImages(
    dto: CreatedProductDto,
    timestamp: number,
  ): Promise<{ imageEntries: string[]; imagesUploadUrls: any[] }> {
    const imageEntries: string[] = [];
    const imagesUploadUrls: any[] = [];

    if (dto.imagesMediaTypes && dto.imagesMediaTypes.length > 0) {
      this.logger.log(`Processing ${dto.imagesMediaTypes.length} product images`);

      for (let i = 0; i < dto.imagesMediaTypes.length; i++) {
        const mediaType = dto.imagesMediaTypes[i];
        
        // Tạo S3 key cho hình ảnh
        const imageKey = generateS3Key({
          baseFolder: 'business',
          categoryFolder: CategoryFolderEnum.IMAGE,
          fileName: `product-image-${i}-${timestamp}`,
          useTimeFolder: true,
        });

        // Thêm vào danh sách images
        imageEntries.push(imageKey);

        // Tạo presigned URL cho upload
        const uploadUrl = await this.s3Service.createPresignedWithID(
          imageKey,
          TimeIntervalEnum.FIFTEEN_MINUTES,
          ImageTypeEnum[mediaType], // Default image type
          FileSizeEnum.FIVE_MB,
        );
        
        imagesUploadUrls.push({
          url: uploadUrl,
          key: imageKey,
          type: mediaType,
          position: i,
        });
      }
    }

    return { imageEntries, imagesUploadUrls };
  }

  /**
   * Xây dựng response cuối cùng cho việc tạo sản phẩm
   */
  async buildCreateResponse(
    product: UserProduct,
    uploadUrls: any,
    additionalInfo: any,
    createDto: CreatedProductDto,
  ): Promise<ProductResponseDto> {
    // Chuyển đổi entity sang DTO response format
    const response = await this.userProductHelper.mapToProductResponseDto(product);

    // Thêm upload URLs nếu có (để frontend upload hình ảnh lên S3)
    if (uploadUrls) {
      response.uploadUrls = uploadUrls;
    }

    // Thêm thông tin bổ sung dựa trên loại sản phẩm
    if (createDto.productType === ProductTypeEnum.PHYSICAL && additionalInfo) {
      // Sản phẩm vật lý: thêm inventory và classifications
      if (additionalInfo.inventory) {
        response.inventory = additionalInfo.inventory;
      }
      if (additionalInfo.classifications) {
        response.classifications = additionalInfo.classifications;
      }
    } else if (additionalInfo) {
      // Các loại sản phẩm khác: thêm advancedInfo và classifications
      if (additionalInfo.advancedInfo) {
        response.advancedInfo = additionalInfo.advancedInfo;
      }
      if (additionalInfo.classifications) {
        response.classifications = additionalInfo.classifications;
      }
    }

    return response;
  }

  /**
   * Lưu sản phẩm vào database
   */
  async saveProduct(product: UserProduct): Promise<UserProduct> {
    return await this.userProductRepository.save(product);
  }

  /**
   * Lấy sản phẩm theo ID sau khi tạo
   */
  async getProductById(productId: number): Promise<UserProduct> {
    const product = await this.userProductRepository.findById(productId);
    if (!product) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
        `Cannot retrieve product after creation: ${productId}`,
      );
    }
    return product;
  }
}
