import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import {
  UserProductRepository,
  ProductAdvancedInfoRepository,
} from '@modules/business/repositories';
import {
  BusinessUpdateProductDto,
} from '../../../dto';
import { ProductTypeEnum } from '@modules/business/enums';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { UserProduct } from '@modules/business/entities';
import { UpdateProductResult } from './update-product-orchestrator';

/**
 * Processor chuyên xử lý cập nhật sản phẩm dịch vụ (SERVICE)
 * Xử lý service packages, service metadata, duration, provider
 */
@Injectable()
export class ServiceProductUpdateProcessor {
  private readonly logger = new Logger(ServiceProductUpdateProcessor.name);

  constructor(
    private readonly userProductRepository: UserProductRepository,
    private readonly productAdvancedInfoRepository: ProductAdvancedInfoRepository,
  ) {}

  /**
   * Cập nhật sản phẩm dịch vụ hoàn chỉnh
   */
  @Transactional()
  async updateServiceProduct(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<UpdateProductResult> {
    this.logger.log(`Updating SERVICE product: ${product.name} (ID: ${product.id})`);

    // BƯỚC 1: Validate dữ liệu đầu vào cho dịch vụ
    await this.validateServiceProductData(updateDto);

    // BƯỚC 2: Cập nhật service-specific fields
    this.updateServiceFields(product, updateDto);

    // BƯỚC 3: Xử lý service metadata
    this.updateServiceMetadata(product, updateDto);

    // BƯỚC 4: Đảm bảo shipment config = 0 cho dịch vụ
    this.ensureZeroShipmentConfig(product);

    // BƯỚC 5: Lưu sản phẩm đã cập nhật
    const updatedProduct = await this.userProductRepository.save(product);

    // BƯỚC 6: Xử lý service packages trong advanced info
    await this.processServicePackagesUpdate(updatedProduct, updateDto);

    return {
      product: updatedProduct,
      imagesUploadUrls: [],
      advancedImagesUploadUrls: [],
      classificationUploadUrls: [],
      classifications: [],
      inventory: null, // Service products don't have inventory
    };
  }

  /**
   * Validate dữ liệu đầu vào cho dịch vụ
   */
  private async validateServiceProductData(updateDto: BusinessUpdateProductDto): Promise<void> {
    const serviceAdvancedInfo = updateDto.advancedInfo as any;

    // Validate purchase count nếu có
    if (serviceAdvancedInfo?.purchaseCount !== undefined && serviceAdvancedInfo.purchaseCount < 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Purchase count không thể âm',
      );
    }

    // Validate service packages nếu có trong advanced info
    if (serviceAdvancedInfo?.servicePackages) {
      await this.validateServicePackages(serviceAdvancedInfo.servicePackages);
    }

    // Validate service metadata fields
    await this.validateServiceMetadata(updateDto);
  }

  /**
   * Validate service packages
   */
  private async validateServicePackages(servicePackages: any[]): Promise<void> {
    if (!Array.isArray(servicePackages)) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Service packages phải là một mảng',
      );
    }

    for (const servicePackage of servicePackages) {
      // Validate required fields
      if (!servicePackage.name) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Service package phải có tên',
        );
      }

      // Validate price nếu có
      if (servicePackage.price) {
        if (!servicePackage.price.listPrice || servicePackage.price.listPrice < 0) {
          throw new AppException(
            BUSINESS_ERROR_CODES.INVALID_INPUT,
            'Giá service package không hợp lệ',
          );
        }
      }

      // Validate duration format nếu có
      if (servicePackage.duration && typeof servicePackage.duration !== 'string') {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Duration phải là chuỗi ký tự',
        );
      }

      // Validate features nếu có
      if (servicePackage.features && !Array.isArray(servicePackage.features)) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Features phải là một mảng',
        );
      }
    }
  }

  /**
   * Validate service metadata
   */
  private async validateServiceMetadata(updateDto: BusinessUpdateProductDto): Promise<void> {
    const serviceAdvancedInfo = updateDto.advancedInfo as any;

    if (serviceAdvancedInfo) {
      // Validate service time format
      if (serviceAdvancedInfo.serviceTime && typeof serviceAdvancedInfo.serviceTime !== 'string') {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Service time phải là chuỗi ký tự',
        );
      }

      // Validate service duration format
      if (serviceAdvancedInfo.serviceDuration && typeof serviceAdvancedInfo.serviceDuration !== 'string') {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Service duration phải là chuỗi ký tự',
        );
      }

      // Validate service provider
      if (serviceAdvancedInfo.serviceProvider && typeof serviceAdvancedInfo.serviceProvider !== 'string') {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Service provider phải là chuỗi ký tự',
        );
      }

      // Validate service type
      if (serviceAdvancedInfo.serviceType && typeof serviceAdvancedInfo.serviceType !== 'string') {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Service type phải là chuỗi ký tự',
        );
      }

      // Validate service location
      if (serviceAdvancedInfo.serviceLocation && typeof serviceAdvancedInfo.serviceLocation !== 'string') {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Service location phải là chuỗi ký tự',
        );
      }
    }
  }

  /**
   * Cập nhật các trường đặc thù cho dịch vụ
   */
  private updateServiceFields(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): void {
    const serviceAdvancedInfo = updateDto.advancedInfo as any;

    // Cập nhật purchase count
    if (serviceAdvancedInfo?.purchaseCount !== undefined) {
      product.metadata = product.metadata || {};
      product.metadata.purchaseCount = serviceAdvancedInfo.purchaseCount;
      this.logger.log(`Updated purchase count to: ${serviceAdvancedInfo.purchaseCount}`);
    }
  }

  /**
   * Cập nhật service metadata
   */
  private updateServiceMetadata(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): void {
    const serviceAdvancedInfo = updateDto.advancedInfo as any;

    if (serviceAdvancedInfo) {
      product.metadata = product.metadata || {};

      // Cập nhật service time
      if (serviceAdvancedInfo.serviceTime !== undefined) {
        product.metadata.serviceTime = serviceAdvancedInfo.serviceTime;
        this.logger.log(`Updated service time to: ${serviceAdvancedInfo.serviceTime}`);
      }

      // Cập nhật service duration
      if (serviceAdvancedInfo.serviceDuration !== undefined) {
        product.metadata.serviceDuration = serviceAdvancedInfo.serviceDuration;
        this.logger.log(`Updated service duration to: ${serviceAdvancedInfo.serviceDuration}`);
      }

      // Cập nhật service provider
      if (serviceAdvancedInfo.serviceProvider !== undefined) {
        product.metadata.serviceProvider = serviceAdvancedInfo.serviceProvider;
        this.logger.log(`Updated service provider to: ${serviceAdvancedInfo.serviceProvider}`);
      }

      // Cập nhật service type
      if (serviceAdvancedInfo.serviceType !== undefined) {
        product.metadata.serviceType = serviceAdvancedInfo.serviceType;
        this.logger.log(`Updated service type to: ${serviceAdvancedInfo.serviceType}`);
      }

      // Cập nhật service location
      if (serviceAdvancedInfo.serviceLocation !== undefined) {
        product.metadata.serviceLocation = serviceAdvancedInfo.serviceLocation;
        this.logger.log(`Updated service location to: ${serviceAdvancedInfo.serviceLocation}`);
      }
    }
  }

  /**
   * Đảm bảo shipment config = 0 cho dịch vụ
   */
  private ensureZeroShipmentConfig(product: UserProduct): void {
    product.shipmentConfig = {
      widthCm: 0,
      heightCm: 0,
      lengthCm: 0,
      weightGram: 0,
    };
    this.logger.log('Set shipment config to zero for SERVICE product');
  }

  /**
   * Xử lý cập nhật service packages trong advanced info
   */
  private async processServicePackagesUpdate(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): Promise<void> {
    const serviceAdvancedInfo = updateDto.advancedInfo as any;

    if (!serviceAdvancedInfo?.servicePackages || !product.detail_id) {
      return;
    }

    try {
      this.logger.log(`Updating service packages for SERVICE product ${product.id}`);

      // Tìm advanced info hiện tại
      const existingAdvancedInfo = await this.productAdvancedInfoRepository.findOne({
        where: { id: product.detail_id }
      });

      if (existingAdvancedInfo) {
        // Xử lý service packages
        const processedServicePackages = serviceAdvancedInfo.servicePackages.map((servicePackage: any) => {
          // Loại bỏ imagesMediaTypes nếu có
          const { imagesMediaTypes, ...packageWithoutImages } = servicePackage;
          return packageWithoutImages;
        });

        // Cập nhật service packages trong advanced info
        existingAdvancedInfo.servicePackages = processedServicePackages;
        existingAdvancedInfo.updatedAt = Date.now();

        await this.productAdvancedInfoRepository.save(existingAdvancedInfo);
        this.logger.log(`Successfully updated service packages for SERVICE product ${product.id}`);
      }
    } catch (error) {
      this.logger.error(`Error updating service packages for SERVICE product ${product.id}: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_UPDATE_FAILED,
        `Lỗi khi cập nhật service packages: ${error.message}`,
      );
    }
  }


}
