# Implementation: Inventory Info Type và Create Inventory Method

## Tóm tắt thay đổi
Đã tạo type cho thông tin tồn kho chi tiết và implement method `createInventoryForPhysicalProduct` để tạo các bản ghi inventory với thông tin chi tiết trong field `info` (jsonb).

## Các file đã được tạo/cập nhật

### 1. Type Definition - Inventory Info
**File:** `src/modules/business/types/inventory-info.type.ts`

#### Interfaces chính:
- **`InventoryInfo`**: Interface chính cho field `info` trong inventory entity
- **`InventoryBatch`**: Interface cho từng batch/lot tồn kho
- **`CreateInventoryInfoInput`**: Type helper để tạo inventory info mới

#### Tính năng:
- **Quản lý batch/lot**: Hỗ trợ nhiều batch trong một inventory
- **Thông tin chi tiết**: SKU, barcode, số lượng, ng<PERSON><PERSON> sản xu<PERSON>, hết hạn
- **Vị trí kho**: Zone, aisle, shelf, position
- **Nhà cung cấp**: Thông tin supplier, invoice number
- **Trạng thái**: ACTIVE, EXPIRED, DAMAGED, RECALLED
- **Metadata**: Thông tin bổ sung linh hoạt

#### Utility Class:
- **`InventoryInfoUtils`**: Các method helper để làm việc với InventoryInfo
  - `createInitialInfo()`: Tạo inventory info với batch đầu tiên
  - `calculateTotalQuantities()`: Tính tổng số lượng từ tất cả batch
  - `addBatch()`: Thêm batch mới
  - `updateBatch()`: Cập nhật batch
  - `removeBatch()`: Xóa batch

### 2. Type Export
**File:** `src/modules/business/types/index.ts`
- Export tất cả types từ `inventory-info.type.ts`
- Export custom field config types

### 3. Physical Product Processor Update
**File:** `src/modules/business/user/services/processors/create/physical-product.processor.ts`

#### Imports mới:
```typescript
import { Inventory } from '@modules/business/entities';
import { InventoryInfo, InventoryInfoUtils } from '@modules/business/types';
import { ProductInventoryDto } from '../../../dto/product-inventory.dto';
```

#### Methods mới:

##### `createInventoryForPhysicalProduct()`
- **Mục đích**: Tạo bản ghi inventory cho sản phẩm vật lý
- **Tham số**: `productId`, `inventoryDto`, `userId`
- **Logic**:
  - Kiểm tra sử dụng inventory có sẵn (`inventoryId`) hoặc tạo mới
  - Thiết lập các field cơ bản: `productId`, `warehouseId`, `sku`, `barcode`
  - Tính toán số lượng: `availableQuantity`, `currentQuantity`, `totalQuantity`
  - Tạo `InventoryInfo` với batch đầu tiên sử dụng `InventoryInfoUtils`
  - Lưu vào database

##### `useExistingInventory()`
- **Mục đích**: Sử dụng inventory có sẵn
- **Logic**:
  - Tìm inventory theo ID
  - Kiểm tra inventory chưa được sử dụng cho sản phẩm khác
  - Cập nhật `productId` nếu chưa có

#### Cập nhật logic xử lý:
```typescript
// Thay thế placeholder bằng implementation thực tế
const inventories = await Promise.all(
  inventoryDtos.map(inventoryDto =>
    this.createInventoryForPhysicalProduct(productId, inventoryDto, userId)
  )
);
```

## Cấu trúc dữ liệu Inventory Info

### Ví dụ InventoryInfo trong field `info`:
```json
{
  "batches": [
    {
      "batchId": "batch_1704067200000_abc123def",
      "sku": "SKU-001",
      "barcode": "1234567890123",
      "currentQuantity": 100,
      "totalQuantity": 100,
      "availableQuantity": 100,
      "reservedQuantity": 0,
      "defectiveQuantity": 0,
      "manufacturingDate": 1704067200000,
      "expiryDate": 1735689600000,
      "lotNumber": "LOT-2024-001",
      "costPrice": {
        "amount": 50000,
        "currency": "VND"
      },
      "location": {
        "zone": "A",
        "aisle": "A1",
        "shelf": "A1-01",
        "position": "A1-01-001"
      },
      "supplier": {
        "supplierId": 123,
        "supplierName": "Nhà cung cấp ABC",
        "invoiceNumber": "INV-2024-001"
      },
      "status": "ACTIVE",
      "notes": "Initial inventory for product 1",
      "createdAt": 1704067200000,
      "updatedAt": 1704067200000
    }
  ],
  "metadata": {
    "notes": "Created for product 1 at warehouse 1",
    "status": "ACTIVE",
    "lastAuditDate": 1704067200000,
    "lastAuditBy": 1
  }
}
```

## Error Handling
Sử dụng các error codes có sẵn từ `BUSINESS_ERROR_CODES`:
- `INVENTORY_NOT_FOUND`: Khi không tìm thấy inventory
- `INVENTORY_ALREADY_USED`: Khi inventory đã được sử dụng cho sản phẩm khác
- `INVENTORY_CREATION_FAILED`: Khi tạo inventory thất bại
- `INVENTORY_VALIDATION_FAILED`: Khi validation thất bại

## Tính năng nổi bật

### 1. Quản lý Batch/Lot
- Hỗ trợ nhiều batch trong một inventory
- Theo dõi ngày sản xuất, hết hạn
- Quản lý số lô sản xuất

### 2. Vị trí trong kho
- Zone, aisle, shelf, position
- Dễ dàng tìm kiếm và quản lý vị trí

### 3. Thông tin nhà cung cấp
- Liên kết với supplier
- Theo dõi invoice number

### 4. Trạng thái linh hoạt
- ACTIVE, EXPIRED, DAMAGED, RECALLED
- Dễ dàng quản lý lifecycle của inventory

### 5. Metadata mở rộng
- Hỗ trợ thêm thông tin tùy chỉnh
- Linh hoạt cho các yêu cầu tương lai

## Tương thích ngược
- Giữ nguyên cấu trúc entity Inventory hiện tại
- Chỉ thêm thông tin vào field `info` (jsonb)
- Không ảnh hưởng đến logic hiện tại

## Sử dụng
```typescript
// Tạo inventory với thông tin chi tiết
const inventory = await this.createInventoryForPhysicalProduct(
  productId,
  {
    warehouseId: 1,
    availableQuantity: 100,
    sku: 'SKU-001',
    barcode: '1234567890123'
  },
  userId
);

// Inventory sẽ có field info chứa InventoryInfo với batch đầu tiên
```
