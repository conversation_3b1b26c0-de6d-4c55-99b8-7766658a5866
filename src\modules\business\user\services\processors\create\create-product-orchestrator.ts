import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { BusinessProductResponseDto as ProductResponseDto } from '../../../dto';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { CreateProductProcessor } from './create-product.processor';
import { PhysicalProductProcessor } from './physical-product.processor';
import { DigitalProductProcessor } from './digital-product.processor';
import { EventProductProcessor } from './event-product.processor';
import { ServiceProductProcessor } from './service-product.processor';
import { ComboProductProcessor } from './combo-product.processor';
import { CreatedProductDto, isComboProductDto, isDigitalProductDto, isEventProductDto, isPhysicalProductDto, isServiceProductDto } from '../../../dto/request/create-products.dto';

/**
 * <PERSON><PERSON> ch<PERSON>h cho việc tạo sản phẩm
 * Điều phối toàn bộ luồng create từ các processor nhỏ
 */
@Injectable()
export class CreateProductOrchestrator {
  private readonly logger = new Logger(CreateProductOrchestrator.name);

  constructor(
    private readonly createProcessor: CreateProductProcessor,
    private readonly physicalProcessor: PhysicalProductProcessor,
    private readonly digitalProcessor: DigitalProductProcessor,
    private readonly eventProcessor: EventProductProcessor,
    private readonly serviceProcessor: ServiceProductProcessor,
    private readonly comboProcessor: ComboProductProcessor,
  ) {}

  /**
   * Method chính để tạo sản phẩm
   * Thay thế cho method createProduct cũ trong UserProductService
   */
  @Transactional()
  async createProduct(
    createProductDto: CreatedProductDto,
    userId: number,
  ): Promise<ProductResponseDto> {
    this.logger.log(`Creating product: ${createProductDto.name} of type: ${createProductDto.productType}`);

    try {
      // BƯỚC 1: Validate chung và xác định loại sản phẩm
      await this.createProcessor.validateCommonFields(createProductDto);

      // BƯỚC 2: Routing theo loại sản phẩm và xử lý tạo entity
      const productResult = await this.routeProductCreation(createProductDto, userId);

      // BƯỚC 3: Xây dựng response cuối cùng
      const response = await this.createProcessor.buildCreateResponse(
        productResult.product,
        productResult.uploadUrls,
        productResult.additionalInfo,
        createProductDto
      );

      this.logger.log(`Successfully created product: ${createProductDto.name} with ID: ${productResult.product.id}`);
      return response;

    } catch (error) {
      // Log lỗi chi tiết
      this.logger.error(`Failed to create product: ${createProductDto.name}`, error.stack);

      // Ném lại AppException nếu đã là AppException
      if (error instanceof AppException) {
        throw error;
      }

      // Wrap lỗi khác thành AppException
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `Failed to create product: ${error.message}`,
      );
    }
  }

  /**
   * Route việc tạo sản phẩm theo loại cụ thể
   */
  private async routeProductCreation(
    createProductDto: CreatedProductDto,
    userId: number,
  ): Promise<CreateProductResult> {
    // Sử dụng type guards để xác định loại sản phẩm và xử lý tương ứng
    if (isPhysicalProductDto(createProductDto)) {
      // Xử lý sản phẩm vật lý (có inventory và shipment)
      return await this.physicalProcessor.createPhysicalProduct(createProductDto, userId);
      
    } else if (isDigitalProductDto(createProductDto)) {
      // Xử lý sản phẩm số (có variants, không có shipment)
      return await this.digitalProcessor.createDigitalProduct(createProductDto, userId);
      
    } else if (isEventProductDto(createProductDto)) {
      // Xử lý sản phẩm sự kiện (có ticket types, không có shipment)
      return await this.eventProcessor.createEventProduct(createProductDto, userId);
      
    } else if (isServiceProductDto(createProductDto)) {
      // Xử lý sản phẩm dịch vụ (có service packages, không có shipment)
      return await this.serviceProcessor.createServiceProduct(createProductDto, userId);
      
    } else if (isComboProductDto(createProductDto)) {
      // Xử lý sản phẩm combo (kết hợp nhiều sản phẩm)
      return await this.comboProcessor.createComboProduct(createProductDto, userId);
      
    } else {
      // Loại sản phẩm không được hỗ trợ
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_PRODUCT_TYPE,
        `Unsupported product type: ${(createProductDto as any).productType}`,
      );
    }
  }
}

/**
 * Interface cho kết quả tạo sản phẩm
 */
export interface CreateProductResult {
  product: any;
  uploadUrls: any;
  additionalInfo: {
    inventory?: any;
    advancedInfo?: any;
    classifications?: any;
  };
}

/**
 * Interface cho context tạo sản phẩm
 */
export interface CreateProductContext {
  userId: number;
  createDto: CreatedProductDto;
  timestamp: number;
}

/**
 * Enum cho các bước tạo sản phẩm
 */
export enum CreateStep {
  VALIDATE_COMMON = 'validate_common',
  ROUTE_CREATION = 'route_creation',
  VALIDATE_SPECIFIC = 'validate_specific',
  PROCESS_CUSTOM_FIELDS = 'process_custom_fields',
  CREATE_BASE_PRODUCT = 'create_base_product',
  PROCESS_IMAGES = 'process_images',
  PROCESS_SPECIFIC_DATA = 'process_specific_data',
  PROCESS_CLASSIFICATIONS = 'process_classifications',
  BUILD_RESPONSE = 'build_response',
}

/**
 * Interface cho tracking progress
 */
export interface CreateProgress {
  currentStep: CreateStep;
  completedSteps: CreateStep[];
  totalSteps: number;
  errors: string[];
  warnings: string[];
}
