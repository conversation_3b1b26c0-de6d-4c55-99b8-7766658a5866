import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsNotEmpty, ValidateNested } from 'class-validator';
import { CreatedProductDto } from './create-products.dto';

/**
 * DTO cho việc tạo nhiều sản phẩm cùng lúc
 * Sử dụng CreatedProductDto union type để hỗ trợ tất cả loại sản phẩm
 */
export class BatchCreateProductsDto {
  @ApiProperty({
    description: 'Danh sách sản phẩm cần tạo',
    type: 'array',
    items: {
      oneOf: [
        { $ref: '#/components/schemas/PhysicalProductCreateDto' },
        { $ref: '#/components/schemas/DigitalProductCreateDto' },
        { $ref: '#/components/schemas/EventProductCreateDto' },
        { $ref: '#/components/schemas/ServiceProductCreateDto' },
        { $ref: '#/components/schemas/ComboProductCreateDto' }
      ]
    },
    example: [
      {
        name: 'Áo thun nam',
        productType: 'PHYSICAL',
        typePrice: 'HAS_PRICE',
        price: {
          listPrice: 300000,
          salePrice: 250000,
          currency: 'VND'
        },
        description: 'Áo thun nam chất liệu cotton cao cấp',
        imagesMediaTypes: ['image/jpeg'],
        tags: ['thời trang', 'nam'],
        shipmentConfig: {
          widthCm: 20,
          heightCm: 5,
          lengthCm: 25,
          weightGram: 150
        },
        inventory: {
          availableQuantity: 100,
          sku: 'SHIRT-001',
          barcode: '1234567890123'
        }
      },
      {
        name: 'Khóa học lập trình',
        productType: 'DIGITAL',
        typePrice: 'HAS_PRICE',
        price: {
          listPrice: 500000,
          salePrice: 400000,
          currency: 'VND'
        },
        description: 'Khóa học lập trình từ cơ bản đến nâng cao',
        imagesMediaTypes: ['image/jpeg'],
        tags: ['giáo dục', 'lập trình'],
        deliveryMethod: 'EMAIL',
        deliveryTiming: 'IMMEDIATE',
        outputType: 'ACCESS_CODE',
        classifications: [
          {
            name: 'Gói cơ bản',
            price: {
              listPrice: 400000,
              salePrice: 300000,
              currency: 'VND'
            },
            description: 'Gói học cơ bản',
            metadata: {
              duration: '30 ngày',
              support: 'Email'
            }
          }
        ]
      }
    ]
  })
  @IsArray()
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => Object) // Sử dụng Object vì CreatedProductDto là union type
  products: CreatedProductDto[];
}
