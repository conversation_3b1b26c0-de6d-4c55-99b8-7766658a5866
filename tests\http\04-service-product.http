### Test Service Product Creation
### Tạo dịch vụ với đầy đủ thông tin

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "<PERSON><PERSON>ch vụ thiết kế website",
  "productType": "SERVICE",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 5000000,
    "salePrice": 4000000,
    "currency": "VND"
  },
  "description": "Dịch vụ thiết kế website chuyên nghiệp cho doanh nghiệ<PERSON>, bao gồm thiết kế UI/UX và lập trình frontend",
  "imagesMediaTypes": ["image/jpeg", "image/png"],
  "tags": ["thiết kế", "website", "ui/ux", "frontend"],
  "purchaseCount": 0,
  "servicePackages": [
    {
      "name": "Gói cơ bản",
      "price": {
        "listPrice": 3000000,
        "salePrice": 2500000,
        "currency": "VND"
      },
      "description": "Website cơ bản 5 trang",
      "duration": "2 tuần",
      "features": ["5 trang", "Responsive design", "SEO cơ bản", "1 tháng support"],
      "metadata": {
        "deliveryTime": "14 days",
        "revisions": "2",
        "hosting": "Not included"
      }
    },
    {
      "name": "Gói tiêu chuẩn",
      "price": {
        "listPrice": 5000000,
        "salePrice": 4000000,
        "currency": "VND"
      },
      "description": "Website tiêu chuẩn 10 trang",
      "duration": "3 tuần",
      "features": ["10 trang", "Responsive design", "SEO nâng cao", "CMS", "3 tháng support"],
      "metadata": {
        "deliveryTime": "21 days",
        "revisions": "3",
        "hosting": "1 year included"
      }
    },
    {
      "name": "Gói cao cấp",
      "price": {
        "listPrice": 8000000,
        "salePrice": 7000000,
        "currency": "VND"
      },
      "description": "Website cao cấp không giới hạn trang",
      "duration": "4 tuần",
      "features": ["Unlimited pages", "Custom design", "Advanced SEO", "CMS", "E-commerce", "6 tháng support"],
      "metadata": {
        "deliveryTime": "28 days",
        "revisions": "Unlimited",
        "hosting": "2 years included"
      }
    }
  ],
  "classifications": [
    {
      "name": "Website doanh nghiệp",
      "price": {
        "listPrice": 6000000,
        "salePrice": 5000000,
        "currency": "VND"
      },
      "description": "Website cho doanh nghiệp lớn",
      "metadata": {
        "type": "Corporate",
        "complexity": "High",
        "timeline": "4-6 weeks"
      }
    },
    {
      "name": "Website cá nhân",
      "price": {
        "listPrice": 2000000,
        "salePrice": 1500000,
        "currency": "VND"
      },
      "description": "Website portfolio cá nhân",
      "metadata": {
        "type": "Personal",
        "complexity": "Low",
        "timeline": "1-2 weeks"
      }
    }
  ]
}

### Test Service Product với giá phân loại

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Dịch vụ tư vấn marketing",
  "productType": "SERVICE",
  "typePrice": "CLASSIFICATION_PRICE",
  "description": "Dịch vụ tư vấn marketing cho doanh nghiệp các quy mô",
  "imagesMediaTypes": ["image/jpeg"],
  "tags": ["tư vấn", "marketing", "strategy", "consulting"],
  "purchaseCount": 0,
  "servicePackages": [
    {
      "name": "Tư vấn cơ bản",
      "price": {
        "listPrice": 2000000,
        "salePrice": 1500000,
        "currency": "VND"
      },
      "description": "Tư vấn marketing cơ bản",
      "duration": "1 tuần",
      "features": ["Phân tích thị trường", "Đề xuất chiến lược", "1 buổi meeting"],
      "metadata": {
        "sessions": "2",
        "deliverables": "Strategy document"
      }
    }
  ],
  "classifications": [
    {
      "name": "Doanh nghiệp nhỏ",
      "price": {
        "listPrice": 3000000,
        "salePrice": 2500000,
        "currency": "VND"
      },
      "description": "Tư vấn cho doanh nghiệp nhỏ",
      "metadata": {
        "companySize": "1-50 employees",
        "duration": "2 weeks",
        "support": "Email"
      }
    },
    {
      "name": "Doanh nghiệp vừa",
      "price": {
        "listPrice": 6000000,
        "salePrice": 5000000,
        "currency": "VND"
      },
      "description": "Tư vấn cho doanh nghiệp vừa",
      "metadata": {
        "companySize": "51-200 employees",
        "duration": "4 weeks",
        "support": "Phone + Email"
      }
    },
    {
      "name": "Doanh nghiệp lớn",
      "price": {
        "listPrice": 12000000,
        "salePrice": 10000000,
        "currency": "VND"
      },
      "description": "Tư vấn cho doanh nghiệp lớn",
      "metadata": {
        "companySize": "200+ employees",
        "duration": "8 weeks",
        "support": "Dedicated consultant"
      }
    }
  ]
}

### Test Service Product đơn giản

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Dịch vụ chụp ảnh cưới",
  "productType": "SERVICE",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 8000000,
    "salePrice": 7000000,
    "currency": "VND"
  },
  "description": "Dịch vụ chụp ảnh cưới chuyên nghiệp, bao gồm chụp ảnh cưới và album ảnh",
  "imagesMediaTypes": ["image/jpeg"],
  "tags": ["chụp ảnh", "cưới", "photography", "wedding"],
  "purchaseCount": 0
}

### Test Service Product với multiple packages

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Dịch vụ SEO website",
  "productType": "SERVICE",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 3000000,
    "salePrice": 2500000,
    "currency": "VND"
  },
  "description": "Dịch vụ SEO website giúp tăng thứ hạng trên Google",
  "imagesMediaTypes": ["image/jpeg"],
  "tags": ["seo", "website", "google", "ranking"],
  "purchaseCount": 0,
  "servicePackages": [
    {
      "name": "SEO cơ bản",
      "price": {
        "listPrice": 2000000,
        "salePrice": 1500000,
        "currency": "VND"
      },
      "description": "SEO cơ bản cho website mới",
      "duration": "1 tháng",
      "features": ["Keyword research", "On-page SEO", "Technical SEO", "Monthly report"],
      "metadata": {
        "keywords": "10",
        "pages": "5",
        "reporting": "Monthly"
      }
    },
    {
      "name": "SEO nâng cao",
      "price": {
        "listPrice": 4000000,
        "salePrice": 3500000,
        "currency": "VND"
      },
      "description": "SEO nâng cao cho website có traffic",
      "duration": "3 tháng",
      "features": ["Advanced keyword research", "Content optimization", "Link building", "Competitor analysis", "Bi-weekly report"],
      "metadata": {
        "keywords": "30",
        "pages": "20",
        "reporting": "Bi-weekly"
      }
    },
    {
      "name": "SEO enterprise",
      "price": {
        "listPrice": 8000000,
        "salePrice": 7000000,
        "currency": "VND"
      },
      "description": "SEO enterprise cho website lớn",
      "duration": "6 tháng",
      "features": ["Comprehensive SEO audit", "Custom strategy", "Content creation", "Advanced link building", "Weekly report", "Dedicated account manager"],
      "metadata": {
        "keywords": "100+",
        "pages": "Unlimited",
        "reporting": "Weekly"
      }
    }
  ]
}

### Test Service Product theo giờ

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Dịch vụ lập trình freelance",
  "productType": "SERVICE",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 500000,
    "salePrice": 400000,
    "currency": "VND"
  },
  "description": "Dịch vụ lập trình freelance theo giờ",
  "imagesMediaTypes": ["image/jpeg"],
  "tags": ["lập trình", "freelance", "development", "coding"],
  "purchaseCount": 0,
  "servicePackages": [
    {
      "name": "1 giờ",
      "price": {
        "listPrice": 400000,
        "salePrice": 350000,
        "currency": "VND"
      },
      "description": "Thuê lập trình viên 1 giờ",
      "duration": "1 giờ",
      "features": ["Bug fixing", "Small features", "Code review"],
      "metadata": {
        "hourlyRate": "350000",
        "minHours": "1",
        "technologies": "React, Node.js, Python"
      }
    },
    {
      "name": "Gói 10 giờ",
      "price": {
        "listPrice": 3500000,
        "salePrice": 3000000,
        "currency": "VND"
      },
      "description": "Gói 10 giờ lập trình",
      "duration": "10 giờ",
      "features": ["Feature development", "API integration", "Database design"],
      "metadata": {
        "hourlyRate": "300000",
        "totalHours": "10",
        "discount": "15%"
      }
    }
  ]
}
