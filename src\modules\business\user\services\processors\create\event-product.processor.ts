import { Injectable, Logger } from '@nestjs/common';
import {
  ProductAdvancedInfoRepository,
} from '@modules/business/repositories';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { PriceTypeEnum } from '@modules/business/enums';
import { ClassificationService } from '../../classification.service';
import { CreateProductProcessor } from './create-product.processor';
import { CreateProductResult } from './create-product-orchestrator';
import { S3Service } from '@shared/services/s3.service';
import { EventProductCreateDto, EventFormatEnum } from '../../../dto/request/create/event-product-create.dto';
import { CreateClassificationDto, ClassificationResponseDto } from '../../../dto/classification.dto';

/**
 * Processor chuyên xử lý logic tạo sản phẩm sự kiện
 * <PERSON>óc tách từ UserProductService để tối ưu hóa và dễ maintain
 */
@Injectable()
export class EventProductProcessor {
  private readonly logger = new Logger(EventProductProcessor.name);

  constructor(
    private readonly createProcessor: CreateProductProcessor,
    private readonly productAdvancedInfoRepository: ProductAdvancedInfoRepository,
    private readonly classificationService: ClassificationService,
    private readonly s3Service: S3Service,
  ) {}

  /**
   * Tạo sản phẩm sự kiện hoàn chỉnh
   */
  async createEventProduct(
    dto: EventProductCreateDto,
    userId: number,
  ): Promise<CreateProductResult> {
    this.logger.log(`Creating EVENT product: ${dto.name}`);

    // BƯỚC 1: Validate dữ liệu đầu vào cho sản phẩm sự kiện
    await this.validateEventProductData(dto);

    // BƯỚC 2: Xử lý custom fields
    const { customFields, metadata } = await this.createProcessor.processCustomFields(dto);

    // BƯỚC 3: Tạo sản phẩm cơ bản với shipment config = 0 và xử lý giá đặc biệt
    const product = await this.createProcessor.createBaseProduct(dto, userId, metadata);
    product.shipmentConfig = { widthCm: 0, heightCm: 0, lengthCm: 0, weightGram: 0 };

    // Sản phẩm sự kiện có thể có giá null (giá sẽ lấy từ ticket types)
    product.typePrice = dto.typePrice;
    if (!dto.price) {
      product.price = null;
      this.logger.log('EVENT product - setting price to null, price will be calculated from ticket types');
    } else {
      product.price = dto.price;
    }

    // BƯỚC 4: Lưu sản phẩm để có ID
    const savedProduct = await this.createProcessor.saveProduct(product);

    // BƯỚC 5: Xử lý hình ảnh sản phẩm chính
    const { imageEntries, imagesUploadUrls } = await this.createProcessor.processProductImages(dto, Date.now());

    // BƯỚC 6: Cập nhật sản phẩm với thông tin hình ảnh
    savedProduct.images = imageEntries;
    await this.createProcessor.saveProduct(savedProduct);

    // BƯỚC 7: Tạo advanced info (ticket types)
    const advancedInfo = await this.createAdvancedInfo(savedProduct.id, dto.productType, dto, []);

    // BƯỚC 8: Xử lý hình ảnh cho ticket types
    const advancedImagesUploadUrls = await this.createAdvancedImagesUploadUrls(dto, dto.productType, Date.now());

    // BƯỚC 9: Cập nhật detail_id để liên kết với advanced info
    if (advancedInfo?.id) {
      savedProduct.detail_id = advancedInfo.id;
      await this.createProcessor.saveProduct(savedProduct);

      // Cập nhật advanced info với image keys nếu có hình ảnh ticket types
      if (advancedImagesUploadUrls.length > 0) {
        await this.updateAdvancedInfoWithImageKeys(advancedInfo.id, dto.productType, advancedImagesUploadUrls);
      }
    }

    // BƯỚC 10: Xử lý classifications
    const classifications = await this.processClassifications(savedProduct.id, dto.classifications || [], userId);

    // BƯỚC 11: Lấy sản phẩm cuối cùng SAU KHI đã cập nhật advanced info với image keys
    const finalProduct = await this.createProcessor.getProductById(savedProduct.id);

    // BƯỚC 12: Tạo object chứa upload URLs cho cả sản phẩm chính và ticket types
    const uploadUrls: any = {
      productId: finalProduct.id.toString(),
      imagesUploadUrls: imagesUploadUrls || []
    };

    // Thêm upload URLs cho hình ảnh ticket types nếu có
    if (advancedImagesUploadUrls && advancedImagesUploadUrls.length > 0) {
      uploadUrls.advancedImagesUploadUrls = advancedImagesUploadUrls;
    }

    return {
      product: finalProduct,
      uploadUrls: (imagesUploadUrls.length > 0 || advancedImagesUploadUrls.length > 0) ? uploadUrls : null,
      additionalInfo: {
        advancedInfo,
        classifications
      }
    };
  }

  /**
   * Validate dữ liệu đầu vào cho sản phẩm sự kiện
   */
  private async validateEventProductData(dto: EventProductCreateDto): Promise<void> {
    // Kiểm tra ticket types có tồn tại không (bắt buộc cho sản phẩm sự kiện)
    if (!dto.ticketTypes || dto.ticketTypes.length === 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Ticket types are required for event products',
      );
    }

    // Validate event format và các trường liên quan
    if (dto.eventFormat === EventFormatEnum.ONLINE && !dto.eventLink) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Event link is required for online events',
      );
    }

    if (dto.eventFormat === EventFormatEnum.OFFLINE && !dto.eventLocation) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Event location is required for offline events',
      );
    }

    if (dto.eventFormat === EventFormatEnum.HYBRID && (!dto.eventLink || !dto.eventLocation)) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Both event link and location are required for hybrid events',
      );
    }

    // Validate thời gian sự kiện
    if (dto.startDate >= dto.endDate) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Start date must be before end date',
      );
    }

    // Validate từng ticket type
    for (const ticketType of dto.ticketTypes) {
      if (ticketType.minQuantityPerPurchase > ticketType.maxQuantityPerPurchase) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
          `Ticket type "${ticketType.name}": minimum quantity cannot be greater than maximum quantity`,
        );
      }
    }

    this.logger.log(`Validated event product data for: ${dto.name}`);
  }

  /**
   * Tạo advanced info cho sản phẩm sự kiện
   */
  private async createAdvancedInfo(
    productId: number,
    productType: string,
    dto: EventProductCreateDto,
    additionalData: any[],
  ): Promise<any> {
    this.logger.log(`Creating advanced info for event product: ${productId}`);

    // Tạo advanced info cho sản phẩm sự kiện với ticket types
    return {
      id: Date.now(), // Temporary ID
      productId,
      productType,
      purchaseCount: 0,
      eventFormat: dto.eventFormat,
      eventLink: dto.eventLink,
      eventLocation: dto.eventLocation,
      startDate: dto.startDate,
      endDate: dto.endDate,
      timezone: dto.timezone,
      ticketTypes: dto.ticketTypes,
    };
  }

  /**
   * Tạo upload URLs cho hình ảnh ticket types
   */
  private async createAdvancedImagesUploadUrls(
    dto: EventProductCreateDto,
    productType: string,
    timestamp: number,
  ): Promise<any[]> {
    const uploadUrls: any[] = [];

    // Tạo presigned URLs cho hình ảnh ticket types
    for (const ticketType of dto.ticketTypes) {
      if (ticketType.imagesMediaTypes && ticketType.imagesMediaTypes.length > 0) {
        for (let i = 0; i < ticketType.imagesMediaTypes.length; i++) {
          const mediaType = ticketType.imagesMediaTypes[i];
          const fileName = `event-ticket-${ticketType.name}-image-${i}-${timestamp}`;

          // TODO: Implement actual S3 presigned URL generation
          uploadUrls.push({
            url: `https://presigned-url-example.com/${fileName}`,
            key: fileName,
            index: i,
            ticketTypeName: ticketType.name
          });
        }
      }
    }

    this.logger.log(`Created ${uploadUrls.length} upload URLs for ticket type images`);
    return uploadUrls;
  }

  /**
   * Cập nhật advanced info với image keys
   */
  private async updateAdvancedInfoWithImageKeys(
    advancedInfoId: number,
    productType: string,
    advancedImagesUploadUrls: any[],
  ): Promise<void> {
    // TODO: Implement logic từ service gốc
    // Cập nhật advanced info với image keys nếu có hình ảnh ticket types
    
    this.logger.log(`Updating advanced info with image keys for ${productType} product: ${advancedInfoId}`);
  }

  /**
   * Xử lý classifications cho sản phẩm
   */
  private async processClassifications(
    productId: number,
    classificationsDto: CreateClassificationDto[],
    userId: number,
  ): Promise<ClassificationResponseDto[]> {
    if (!classificationsDto || classificationsDto.length === 0) {
      return [];
    }

    this.logger.log(`Processing ${classificationsDto.length} classifications for event product: ${productId}`);

    const createdClassifications: ClassificationResponseDto[] = [];

    // Tạo từng classification
    for (const classificationDto of classificationsDto) {
      try {
        // Tạo classification thông qua ClassificationService
        const createdClassification = await this.classificationService.create(
          productId,
          classificationDto,
          userId,
        );

        createdClassifications.push(createdClassification);
        this.logger.log(`Created classification: ${createdClassification.id} for event product: ${productId}`);
      } catch (error) {
        this.logger.error(`Failed to create classification for event product ${productId}: ${error.message}`, error.stack);
        throw error;
      }
    }

    this.logger.log(`Successfully created ${createdClassifications.length} classifications for event product: ${productId}`);
    return createdClassifications;
  }
}
