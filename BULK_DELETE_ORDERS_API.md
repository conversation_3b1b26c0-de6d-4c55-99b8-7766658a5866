# API Xóa Nhiều Đơn Hàng

## Tổng quan
API này cho phép xóa nhiều đơn hàng cùng lúc theo danh sách ID.

## Endpoint
```
DELETE /user/orders/bulk
```

## Authentication
Y<PERSON><PERSON> cầu JWT token trong header:
```
Authorization: Bearer <your_jwt_token>
```

## Request Body
```json
{
  "orderIds": [1, 2, 3, 4, 5]
}
```

### Validation Rules
- `orderIds`: <PERSON><PERSON><PERSON> số nguyên, bắt buộc
- <PERSON>ải có ít nhất 1 ID trong mảng
- Không được có ID trùng lặp
- Mỗi ID phải là số nguyên dương

## Response

### Thành công (200 OK)
```json
{
  "success": true,
  "message": "Xóa thành công 3/5 đơn hàng",
  "data": {
    "totalRequested": 5,
    "successCount": 3,
    "failureCount": 2,
    "results": [
      {
        "orderId": 1,
        "status": "success",
        "message": "Xóa đơn hàng thành công"
      },
      {
        "orderId": 2,
        "status": "success", 
        "message": "Xóa đơn hàng thành công"
      },
      {
        "orderId": 3,
        "status": "success",
        "message": "Xóa đơn hàng thành công"
      },
      {
        "orderId": 4,
        "status": "error",
        "message": "Đơn hàng không tồn tại hoặc không thuộc về bạn"
      },
      {
        "orderId": 5,
        "status": "error",
        "message": "Đơn hàng không tồn tại hoặc không thuộc về bạn"
      }
    ],
    "message": "Xóa thành công 3/5 đơn hàng"
  }
}
```

### Lỗi Validation (400 Bad Request)
```json
{
  "success": false,
  "message": "Danh sách đơn hàng không được để trống",
  "errorCode": "ORDER_BULK_DELETE_VALIDATION_FAILED"
}
```

### Lỗi Xóa Hoàn Toàn Thất Bại (500 Internal Server Error)
```json
{
  "success": false,
  "message": "Xóa thành công 0/3 đơn hàng",
  "errorCode": "ORDER_BULK_DELETE_FAILED"
}
```

### Lỗi Xóa Một Phần (206 Partial Content)
```json
{
  "success": false,
  "message": "Xóa thành công 2/5 đơn hàng",
  "errorCode": "ORDER_BULK_DELETE_PARTIAL_SUCCESS"
}
```

## Quy tắc nghiệp vụ
1. Chỉ có thể xóa đơn hàng thuộc về người dùng hiện tại
2. Đơn hàng không tồn tại sẽ được báo lỗi nhưng không làm dừng quá trình xóa các đơn hàng khác
3. API sử dụng hard delete (xóa vĩnh viễn)
4. Thao tác được thực hiện trong transaction để đảm bảo tính nhất quán

## Ví dụ sử dụng

### Curl
```bash
curl -X DELETE http://localhost:3000/user/orders/bulk \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{"orderIds": [1, 2, 3]}'
```

### JavaScript/Fetch
```javascript
const response = await fetch('/user/orders/bulk', {
  method: 'DELETE',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    orderIds: [1, 2, 3]
  })
});

const result = await response.json();
console.log(result);
```

## Error Codes
- `ORDER_BULK_DELETE_VALIDATION_FAILED` (30042): Validation thất bại
- `ORDER_BULK_DELETE_FAILED` (30040): Xóa hoàn toàn thất bại  
- `ORDER_BULK_DELETE_PARTIAL_SUCCESS` (30041): Xóa một phần thành công
