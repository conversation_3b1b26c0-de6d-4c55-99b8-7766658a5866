import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import {
  UserProductRepository,
  ProductAdvancedInfoRepository,
} from '@modules/business/repositories';
import {
  BusinessUpdateProductDto,
} from '../../../dto';
import { ProductTypeEnum } from '@modules/business/enums';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { UserProduct } from '@modules/business/entities';
import { UpdateProductResult } from './update-product-orchestrator';

/**
 * Processor chuyên xử lý cập nhật sản phẩm combo (COMBO)
 * Xử lý combo items, combo pricing, combo metadata
 */
@Injectable()
export class ComboProductUpdateProcessor {
  private readonly logger = new Logger(ComboProductUpdateProcessor.name);

  constructor(
    private readonly userProductRepository: UserProductRepository,
    private readonly productAdvancedInfoRepository: ProductAdvancedInfoRepository,
  ) {}

  /**
   * Cập nhật sản phẩm combo hoàn chỉnh
   */
  @Transactional()
  async updateComboProduct(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<UpdateProductResult> {
    this.logger.log(`Updating COMBO product: ${product.name} (ID: ${product.id})`);

    // BƯỚC 1: Validate dữ liệu đầu vào cho combo
    await this.validateComboProductData(updateDto);

    // BƯỚC 2: Cập nhật combo-specific fields
    this.updateComboFields(product, updateDto);

    // BƯỚC 3: Xử lý combo metadata
    this.updateComboMetadata(product, updateDto);

    // BƯỚC 4: Đảm bảo shipment config = 0 cho combo
    this.ensureZeroShipmentConfig(product);

    // BƯỚC 5: Lưu sản phẩm đã cập nhật
    const updatedProduct = await this.userProductRepository.save(product);

    // BƯỚC 6: Xử lý combo items trong advanced info
    await this.processComboItemsUpdate(updatedProduct, updateDto);

    return {
      product: updatedProduct,
      imagesUploadUrls: [],
      advancedImagesUploadUrls: [],
      classificationUploadUrls: [],
      classifications: [],
      inventory: null, // Combo products don't have inventory
    };
  }

  /**
   * Validate dữ liệu đầu vào cho combo
   */
  private async validateComboProductData(updateDto: BusinessUpdateProductDto): Promise<void> {
    const comboAdvancedInfo = updateDto.advancedInfo as any;

    // Validate purchase count nếu có
    if (comboAdvancedInfo?.purchaseCount !== undefined && comboAdvancedInfo.purchaseCount < 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Purchase count không thể âm',
      );
    }

    // Validate combo items nếu có trong advanced info
    if (comboAdvancedInfo?.comboItems) {
      await this.validateComboItems(comboAdvancedInfo.comboItems);
    }

    // Validate combo metadata
    await this.validateComboMetadata(updateDto);
  }

  /**
   * Validate combo items
   */
  private async validateComboItems(comboItems: any[]): Promise<void> {
    if (!Array.isArray(comboItems) || comboItems.length === 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Combo phải có ít nhất một sản phẩm',
      );
    }

    for (const comboItem of comboItems) {
      // Validate required fields
      if (!comboItem.productId && !comboItem.productName) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Combo item phải có productId hoặc productName',
        );
      }

      // Validate quantity
      if (!comboItem.quantity || comboItem.quantity <= 0) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Số lượng sản phẩm trong combo phải lớn hơn 0',
        );
      }

      // Validate pricing
      if (comboItem.originalPrice !== undefined && comboItem.originalPrice < 0) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Giá gốc không thể âm',
        );
      }

      if (comboItem.discountedPrice !== undefined && comboItem.discountedPrice < 0) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Giá giảm không thể âm',
        );
      }

      // Validate discount logic
      if (comboItem.originalPrice && comboItem.discountedPrice &&
          comboItem.discountedPrice > comboItem.originalPrice) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          `Giá giảm của "${comboItem.productName}" không thể lớn hơn giá gốc`,
        );
      }
    }
  }

  /**
   * Validate combo metadata
   */
  private async validateComboMetadata(updateDto: BusinessUpdateProductDto): Promise<void> {
    const comboAdvancedInfo = updateDto.advancedInfo as any;

    if (comboAdvancedInfo) {
      // Validate combo type
      if (comboAdvancedInfo.comboType && typeof comboAdvancedInfo.comboType !== 'string') {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Combo type phải là chuỗi ký tự',
        );
      }

      // Validate combo discount percentage
      if (comboAdvancedInfo.discountPercentage !== undefined) {
        if (comboAdvancedInfo.discountPercentage < 0 || comboAdvancedInfo.discountPercentage > 100) {
          throw new AppException(
            BUSINESS_ERROR_CODES.INVALID_INPUT,
            'Phần trăm giảm giá phải từ 0 đến 100',
          );
        }
      }

      // Validate combo validity period
      if (comboAdvancedInfo.validFrom && comboAdvancedInfo.validTo) {
        const validFrom = new Date(comboAdvancedInfo.validFrom);
        const validTo = new Date(comboAdvancedInfo.validTo);

        if (validFrom >= validTo) {
          throw new AppException(
            BUSINESS_ERROR_CODES.INVALID_INPUT,
            'Ngày bắt đầu phải trước ngày kết thúc',
          );
        }
      }
    }
  }

  /**
   * Cập nhật các trường đặc thù cho combo
   */
  private updateComboFields(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): void {
    const comboAdvancedInfo = updateDto.advancedInfo as any;

    // Cập nhật purchase count
    if (comboAdvancedInfo?.purchaseCount !== undefined) {
      product.metadata = product.metadata || {};
      product.metadata.purchaseCount = comboAdvancedInfo.purchaseCount;
      this.logger.log(`Updated purchase count to: ${comboAdvancedInfo.purchaseCount}`);
    }
  }

  /**
   * Cập nhật combo metadata
   */
  private updateComboMetadata(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): void {
    const comboAdvancedInfo = updateDto.advancedInfo as any;

    if (comboAdvancedInfo) {
      product.metadata = product.metadata || {};

      // Cập nhật combo type
      if (comboAdvancedInfo.comboType !== undefined) {
        product.metadata.comboType = comboAdvancedInfo.comboType;
        this.logger.log(`Updated combo type to: ${comboAdvancedInfo.comboType}`);
      }

      // Cập nhật discount percentage
      if (comboAdvancedInfo.discountPercentage !== undefined) {
        product.metadata.discountPercentage = comboAdvancedInfo.discountPercentage;
        this.logger.log(`Updated discount percentage to: ${comboAdvancedInfo.discountPercentage}%`);
      }

      // Cập nhật validity period
      if (comboAdvancedInfo.validFrom !== undefined) {
        product.metadata.validFrom = comboAdvancedInfo.validFrom;
        this.logger.log(`Updated valid from to: ${comboAdvancedInfo.validFrom}`);
      }

      if (comboAdvancedInfo.validTo !== undefined) {
        product.metadata.validTo = comboAdvancedInfo.validTo;
        this.logger.log(`Updated valid to to: ${comboAdvancedInfo.validTo}`);
      }

      // Cập nhật combo description
      if (comboAdvancedInfo.comboDescription !== undefined) {
        product.metadata.comboDescription = comboAdvancedInfo.comboDescription;
        this.logger.log(`Updated combo description`);
      }
    }
  }

  /**
   * Đảm bảo shipment config = 0 cho combo
   */
  private ensureZeroShipmentConfig(product: UserProduct): void {
    product.shipmentConfig = {
      widthCm: 0,
      heightCm: 0,
      lengthCm: 0,
      weightGram: 0,
    };
    this.logger.log('Set shipment config to zero for COMBO product');
  }

  /**
   * Xử lý cập nhật combo items trong advanced info
   */
  private async processComboItemsUpdate(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): Promise<void> {
    const comboAdvancedInfo = updateDto.advancedInfo as any;

    if (!comboAdvancedInfo?.comboItems || !product.detail_id) {
      return;
    }

    try {
      this.logger.log(`Updating combo items for COMBO product ${product.id}`);

      // Tìm advanced info hiện tại
      const existingAdvancedInfo = await this.productAdvancedInfoRepository.findOne({
        where: { id: product.detail_id }
      });

      if (existingAdvancedInfo) {
        // Xử lý combo items
        const processedComboItems = comboAdvancedInfo.comboItems.map((comboItem: any) => {
          // Loại bỏ imagesMediaTypes nếu có
          const { imagesMediaTypes, ...itemWithoutImages } = comboItem;
          return itemWithoutImages;
        });

        // Tính toán tổng giá trị combo
        const totalOriginalPrice = this.calculateTotalOriginalPrice(processedComboItems);
        const totalDiscountedPrice = this.calculateTotalDiscountedPrice(processedComboItems);
        const totalSavings = totalOriginalPrice - totalDiscountedPrice;
        const discountPercentage = totalOriginalPrice > 0 ? (totalSavings / totalOriginalPrice) * 100 : 0;

        // Cập nhật combo trong advanced info (sử dụng field combo có sẵn)
        existingAdvancedInfo.combo = processedComboItems as any;
        existingAdvancedInfo.updatedAt = Date.now();

        await this.productAdvancedInfoRepository.save(existingAdvancedInfo);
        this.logger.log(`Successfully updated combo items for COMBO product ${product.id}`);
      }
    } catch (error) {
      this.logger.error(`Error updating combo items for COMBO product ${product.id}: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_UPDATE_FAILED,
        `Lỗi khi cập nhật combo items: ${error.message}`,
      );
    }
  }

  /**
   * Tính tổng giá gốc của combo
   */
  private calculateTotalOriginalPrice(comboItems: any[]): number {
    return comboItems.reduce((total, item) => {
      return total + ((item.originalPrice || 0) * (item.quantity || 1));
    }, 0);
  }

  /**
   * Tính tổng giá giảm của combo
   */
  private calculateTotalDiscountedPrice(comboItems: any[]): number {
    return comboItems.reduce((total, item) => {
      return total + ((item.discountedPrice || item.originalPrice || 0) * (item.quantity || 1));
    }, 0);
  }


}
