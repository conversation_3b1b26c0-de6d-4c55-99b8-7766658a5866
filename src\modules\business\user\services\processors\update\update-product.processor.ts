import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { plainToInstance } from 'class-transformer';
import {
  UserProductRepository,
  CustomFieldRepository,
  InventoryRepository,
  PhysicalWarehouseRepository,
  ProductAdvancedInfoRepository,
} from '@modules/business/repositories';
import {
  BusinessUpdateProductDto,
  BusinessProductResponseDto as ProductResponseDto,
  ClassificationResponseDto,
  CreateClassificationDto,
  ProductInventoryDto,
} from '../../../dto';
import { ProductTypeEnum, PriceTypeEnum } from '@modules/business/enums';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { UserProduct } from '@modules/business/entities';
import { UserProductHelper } from '../../../helpers/user-product.helper';
import { MetadataHelper } from '../../../helpers/metadata.helper';
import { ValidationHelper } from '../../../helpers/validation.helper';
import { ProductValidationHelper } from '../../../helpers/product-validation.helper';
import { ClassificationService } from '../../classification.service';
import { DataSource } from 'typeorm';
import { InventoryResponseDto } from '../../../dto/inventory';

/**
 * Processor chuyên xử lý logic cập nhật sản phẩm chung
 * Xử lý các logic chung cho tất cả loại sản phẩm
 * Logic chuyên biệt theo loại sản phẩm được xử lý bởi các processors riêng
 */
@Injectable()
export class UpdateProductProcessor {
  private readonly logger = new Logger(UpdateProductProcessor.name);

  constructor(
    private readonly userProductRepository: UserProductRepository,
    private readonly customFieldRepository: CustomFieldRepository,
    private readonly inventoryRepository: InventoryRepository,
    private readonly physicalWarehouseRepository: PhysicalWarehouseRepository,
    private readonly productAdvancedInfoRepository: ProductAdvancedInfoRepository,
    private readonly userProductHelper: UserProductHelper,
    private readonly metadataHelper: MetadataHelper,
    private readonly validationHelper: ValidationHelper,
    private readonly productValidationHelper: ProductValidationHelper,
    private readonly classificationService: ClassificationService,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Tìm và validate sản phẩm tồn tại
   */
  async findAndValidateProduct(
    id: number,
    userId: number,
  ): Promise<UserProduct> {
    const product = await this.userProductRepository.findByIdAndUserId(
      id,
      userId,
    );

    if (!product) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
        `Không tìm thấy sản phẩm với ID ${id}`,
      );
    }

    return product;
  }

  /**
   * Cập nhật thông tin cơ bản của sản phẩm
   */
  updateBasicFields(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): void {
    // Cập nhật name
    if (updateDto.name !== undefined) {
      product.name = updateDto.name;
    }

    // Cập nhật productType
    if (updateDto.productType !== undefined) {
      product.productType = updateDto.productType;
    }

    // Cập nhật description
    if (updateDto.description !== undefined) {
      product.description = updateDto.description;
    }

    // Cập nhật tags
    if (updateDto.tags !== undefined) {
      product.tags = updateDto.tags;
    }

    // Cập nhật shipmentConfig
    if (updateDto.shipmentConfig !== undefined) {
      product.shipmentConfig = updateDto.shipmentConfig;
    }
  }

  /**
   * Xử lý cập nhật giá sản phẩm theo loại
   */
  updateProductPricing(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): void {
    const finalProductType =
      updateDto.productType !== undefined
        ? updateDto.productType
        : product.productType;

    if (finalProductType === ProductTypeEnum.EVENT) {
      // Xử lý đặc biệt cho EVENT products
      product.price = null;
      product.typePrice = product.typePrice || PriceTypeEnum.HAS_PRICE;
      this.logger.log(
        'EVENT product detected in update - setting price to null and keeping typePrice as HAS_PRICE',
      );
    } else {
      // Xử lý cho các loại sản phẩm khác
      this.updateNormalProductPricing(product, updateDto);
    }
  }

  /**
   * Cập nhật giá cho sản phẩm thông thường (không phải EVENT)
   */
  private updateNormalProductPricing(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): void {
    if (updateDto.price !== undefined && updateDto.typePrice !== undefined) {
      // Validate giá khi có cả price và typePrice
      this.validationHelper.validateProductPrice(
        updateDto.price,
        updateDto.typePrice,
      );
      product.price = updateDto.price;
      product.typePrice = updateDto.typePrice;
    } else if (updateDto.price !== undefined) {
      // Chỉ cập nhật price
      product.price = updateDto.price;
    } else if (updateDto.typePrice !== undefined) {
      // Chỉ cập nhật typePrice
      product.typePrice = updateDto.typePrice;
    }
  }

  /**
   * Xử lý cập nhật custom fields
   */
  async updateCustomFields(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): Promise<void> {
    if (updateDto.customFields === undefined) {
      return;
    }

    let customFields: any[] = [];

    if (updateDto.customFields && updateDto.customFields.length > 0) {
      this.logger.log(
        `Xử lý ${updateDto.customFields.length} custom fields cho sản phẩm`,
      );

      // Lấy danh sách ID custom fields
      const customFieldIds = this.metadataHelper.extractCustomFieldIds(
        updateDto.customFields,
      );

      // Lấy thông tin chi tiết custom fields từ database
      customFields = await this.customFieldRepository.findByIds(customFieldIds);

      // Validate custom fields
      this.metadataHelper.validateCustomFieldInputs(
        updateDto.customFields,
        customFields,
      );
    }

    // Xử lý metadata
    const additionalMetadata = this.preserveExistingMetadata(product);
    await this.updateVariantMetadata(product, updateDto, additionalMetadata);
    this.updateServiceMetadata(product, updateDto, additionalMetadata);

    // Cập nhật metadata cho sản phẩm
    const metadata = this.metadataHelper.buildMetadata(
      updateDto.customFields,
      customFields,
      additionalMetadata,
    );
    product.metadata = metadata;
  }

  /**
   * Bảo tồn metadata hiện có
   */
  private preserveExistingMetadata(product: UserProduct): any {
    const additionalMetadata: any = {};

    // Giữ lại variant metadata hiện tại nếu có
    if (product.metadata?.variants) {
      additionalMetadata.variants = product.metadata.variants;
    }

    // Giữ lại service metadata hiện tại nếu có
    const serviceFields = [
      'serviceTime',
      'serviceDuration',
      'serviceProvider',
      'serviceType',
      'serviceLocation',
    ];
    serviceFields.forEach((field) => {
      if (product.metadata?.[field] !== undefined) {
        additionalMetadata[field] = product.metadata[field];
      }
    });

    return additionalMetadata;
  }

  /**
   * Finalize product update
   */
  finalizeProductUpdate(product: UserProduct): void {
    // Đảm bảo các trường embedding vẫn là null
    product.nameEmbedding = null;
    product.descriptionEmbedding = null;
    product.tagsEmbedding = null;

    // Cập nhật thời gian
    product.updatedAt = Date.now();
  }

  /**
   * Lưu sản phẩm đã cập nhật
   */
  async saveUpdatedProduct(product: UserProduct): Promise<UserProduct> {
    return await this.userProductRepository.save(product);
  }

  /**
   * Cập nhật metadata chung (được gọi từ processors chuyên biệt)
   */
  private async updateVariantMetadata(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
    additionalMetadata: any,
  ): Promise<void> {
    // Logic này đã được chuyển sang DigitalProductUpdateProcessor
    // Giữ lại để tương thích với code hiện tại
  }

  /**
   * Cập nhật service metadata (được gọi từ processors chuyên biệt)
   */
  private updateServiceMetadata(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
    additionalMetadata: any,
  ): void {
    // Logic này đã được chuyển sang ServiceProductUpdateProcessor
    // Giữ lại để tương thích với code hiện tại
  }

  /**
   * Xử lý cập nhật advanced info (đã được chuyển sang processors chuyên biệt)
   */
  async processAdvancedInfoUpdate(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
    timestamp: number,
  ): Promise<{ advancedImagesUploadUrls: any[] }> {
    // Logic này đã được chuyển sang các processors chuyên biệt:
    // - DigitalProductUpdateProcessor
    // - EventProductUpdateProcessor
    // - ServiceProductUpdateProcessor
    // - ComboProductUpdateProcessor

    // Giữ lại method này để tương thích với code hiện tại
    return { advancedImagesUploadUrls: [] };
  }

  /**
   * Xử lý cập nhật classifications
   */
  async processClassificationsUpdate(
    productId: number,
    updateDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<{ classifications: ClassificationResponseDto[]; classificationUploadUrls: any[] }> {
    const classifications: ClassificationResponseDto[] = [];
    const classificationUploadUrls: any[] = [];

    if (updateDto.classifications && updateDto.classifications.length > 0) {
      this.logger.log(`Cập nhật ${updateDto.classifications.length} classifications cho sản phẩm ${productId}`);

      for (const classificationDto of updateDto.classifications) {
        try {
          if (classificationDto.id) {
            // Cập nhật classification hiện có
            const updatedClassification = await this.classificationService.update(
              classificationDto.id,
              classificationDto,
              userId,
            );
            classifications.push(updatedClassification);
          } else {
            // Tạo classification mới
            const createClassificationDto: CreateClassificationDto = {
              type: classificationDto.type || '',
              description: '',
              price: classificationDto.price as any,
              customFields: classificationDto.customFields,
              imagesMediaTypes: classificationDto.imagesMediaTypes,
              sku: `SKU-${Date.now()}`,
              minQuantityPerPurchase: classificationDto.minQuantityPerPurchase,
              maxQuantityPerPurchase: classificationDto.maxQuantityPerPurchase,
            };

            const newClassification = await this.classificationService.create(
              productId,
              createClassificationDto,
              userId,
            );
            classifications.push(newClassification);
          }
        } catch (error) {
          this.logger.error(`Lỗi khi xử lý classification: ${error.message}`, error.stack);
          throw error;
        }
      }
    }

    return { classifications, classificationUploadUrls };
  }

  /**
   * Xử lý xóa classifications
   */
  async processClassificationsDeletion(
    productId: number,
    classificationIds: number[],
    userId: number,
  ): Promise<void> {
    if (classificationIds && classificationIds.length > 0) {
      this.logger.log(`Xóa ${classificationIds.length} classifications cho sản phẩm ${productId}`);

      for (const classificationId of classificationIds) {
        try {
          await this.classificationService.delete(classificationId, userId);
        } catch (error) {
          this.logger.error(`Lỗi khi xóa classification ${classificationId}: ${error.message}`, error.stack);
          // Không throw error để tiếp tục xóa các classification khác
        }
      }
    }
  }

  // Các method validate và process inventory đã được chuyển sang PhysicalProductUpdateProcessor

  /**
   * Xây dựng response cuối cùng
   */
  async buildUpdateResponse(
    product: UserProduct,
    imagesUploadUrls: any[],
    advancedImagesUploadUrls: any[],
    classificationUploadUrls: any[],
    classifications: ClassificationResponseDto[],
    inventory: any,
  ): Promise<ProductResponseDto> {
    // Chuyển đổi product entity sang DTO response
    const productDto = await this.userProductHelper.mapToProductResponseDto(product);

    // Thêm thông tin bổ sung vào response
    const response: any = {
      ...productDto,
    };

    // Thêm upload URLs nếu có
    if (imagesUploadUrls.length > 0) {
      response.imagesUploadUrls = imagesUploadUrls;
    }

    if (advancedImagesUploadUrls.length > 0) {
      response.advancedImagesUploadUrls = advancedImagesUploadUrls;
    }

    if (classificationUploadUrls.length > 0) {
      response.classificationUploadUrls = classificationUploadUrls;
    }

    // Thêm classifications nếu có
    if (classifications.length > 0) {
      response.classifications = classifications;
    }

    // Thêm inventory nếu có
    if (inventory) {
      response.inventory = inventory;
    }

    return response;
  }
}
