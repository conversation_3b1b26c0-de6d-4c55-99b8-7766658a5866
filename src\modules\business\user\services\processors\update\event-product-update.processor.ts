import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import {
  UserProductRepository,
  ProductAdvancedInfoRepository,
} from '@modules/business/repositories';
import {
  BusinessUpdateProductDto,
} from '../../../dto';
import { ProductTypeEnum, PriceTypeEnum } from '@modules/business/enums';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { UserProduct } from '@modules/business/entities';
import { UpdateProductResult } from './update-product-orchestrator';

/**
 * Processor chuyên xử lý cập nhật sản phẩm sự kiện (EVENT)
 * Xử lý event date, location, ticket types, max attendees
 */
@Injectable()
export class EventProductUpdateProcessor {
  private readonly logger = new Logger(EventProductUpdateProcessor.name);

  constructor(
    private readonly userProductRepository: UserProductRepository,
    private readonly productAdvancedInfoRepository: ProductAdvancedInfoRepository,
  ) {}

  /**
   * Cập nhật sản phẩm sự kiện hoàn chỉnh
   */
  @Transactional()
  async updateEventProduct(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<UpdateProductResult> {
    this.logger.log(`Updating EVENT product: ${product.name} (ID: ${product.id})`);

    // BƯỚC 1: Validate dữ liệu đầu vào cho sự kiện
    await this.validateEventProductData(updateDto);

    // BƯỚC 2: Cập nhật event-specific fields
    this.updateEventFields(product, updateDto);

    // BƯỚC 3: Xử lý giá đặc biệt cho EVENT (price = null)
    this.updateEventPricing(product, updateDto);

    // BƯỚC 4: Đảm bảo shipment config = 0 cho sự kiện
    this.ensureZeroShipmentConfig(product);

    // BƯỚC 5: Lưu sản phẩm đã cập nhật
    const updatedProduct = await this.userProductRepository.save(product);

    // BƯỚC 6: Xử lý ticket types trong advanced info
    await this.processTicketTypesUpdate(updatedProduct, updateDto);

    return {
      product: updatedProduct,
      imagesUploadUrls: [],
      advancedImagesUploadUrls: [],
      classificationUploadUrls: [],
      classifications: [],
      inventory: null, // Event products don't have inventory
    };
  }

  /**
   * Validate dữ liệu đầu vào cho sự kiện
   */
  private async validateEventProductData(updateDto: BusinessUpdateProductDto): Promise<void> {
    // Validate event date nếu có
    if (updateDto.eventDate) {
      const eventDate = new Date(updateDto.eventDate);
      const now = new Date();
      
      if (eventDate <= now) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Ngày sự kiện phải trong tương lai',
        );
      }
    }

    // Validate max attendees nếu có
    if (updateDto.maxAttendees !== undefined && updateDto.maxAttendees <= 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Số lượng người tham dự tối đa phải lớn hơn 0',
      );
    }

    // Validate purchase count nếu có
    if (updateDto.purchaseCount !== undefined && updateDto.purchaseCount < 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Purchase count không thể âm',
      );
    }

    // Validate ticket types nếu có trong advanced info
    if (updateDto.advancedInfo?.ticketTypes) {
      await this.validateTicketTypes(updateDto.advancedInfo.ticketTypes);
    }
  }

  /**
   * Validate ticket types
   */
  private async validateTicketTypes(ticketTypes: any[]): Promise<void> {
    if (!Array.isArray(ticketTypes) || ticketTypes.length === 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Sự kiện phải có ít nhất một loại vé',
      );
    }

    for (const ticketType of ticketTypes) {
      // Validate required fields
      if (!ticketType.name || !ticketType.price) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Loại vé phải có tên và giá',
        );
      }

      // Validate price
      if (!ticketType.price.listPrice || ticketType.price.listPrice < 0) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Giá vé không hợp lệ',
        );
      }

      // Validate max quantity
      if (ticketType.maxQuantity !== undefined && ticketType.maxQuantity <= 0) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Số lượng vé tối đa phải lớn hơn 0',
        );
      }
    }
  }

  /**
   * Cập nhật các trường đặc thù cho sự kiện
   */
  private updateEventFields(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): void {
    // Cập nhật event date
    if (updateDto.eventDate !== undefined) {
      product.metadata = product.metadata || {};
      product.metadata.eventDate = updateDto.eventDate;
      this.logger.log(`Updated event date to: ${updateDto.eventDate}`);
    }

    // Cập nhật event location
    if (updateDto.eventLocation !== undefined) {
      product.metadata = product.metadata || {};
      product.metadata.eventLocation = updateDto.eventLocation;
      this.logger.log(`Updated event location to: ${updateDto.eventLocation}`);
    }

    // Cập nhật max attendees
    if (updateDto.maxAttendees !== undefined) {
      product.metadata = product.metadata || {};
      product.metadata.maxAttendees = updateDto.maxAttendees;
      this.logger.log(`Updated max attendees to: ${updateDto.maxAttendees}`);
    }

    // Cập nhật purchase count
    if (updateDto.purchaseCount !== undefined) {
      product.metadata = product.metadata || {};
      product.metadata.purchaseCount = updateDto.purchaseCount;
      this.logger.log(`Updated purchase count to: ${updateDto.purchaseCount}`);
    }
  }

  /**
   * Xử lý giá đặc biệt cho EVENT products
   */
  private updateEventPricing(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): void {
    // EVENT products có giá = null, giá thực tế nằm trong ticket types
    product.price = null;
    product.typePrice = product.typePrice || PriceTypeEnum.HAS_PRICE;
    
    this.logger.log('Set price to null for EVENT product (price is in ticket types)');
  }

  /**
   * Đảm bảo shipment config = 0 cho sự kiện
   */
  private ensureZeroShipmentConfig(product: UserProduct): void {
    product.shipmentConfig = {
      widthCm: 0,
      heightCm: 0,
      lengthCm: 0,
      weightGram: 0,
    };
    this.logger.log('Set shipment config to zero for EVENT product');
  }

  /**
   * Xử lý cập nhật ticket types trong advanced info
   */
  private async processTicketTypesUpdate(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): Promise<void> {
    if (!updateDto.advancedInfo?.ticketTypes || !product.detail_id) {
      return;
    }

    try {
      this.logger.log(`Updating ticket types for EVENT product ${product.id}`);

      // Tìm advanced info hiện tại
      const existingAdvancedInfo = await this.productAdvancedInfoRepository.findOne({
        where: { id: product.detail_id }
      });

      if (existingAdvancedInfo) {
        // Xử lý ticket types
        const processedTicketTypes = updateDto.advancedInfo.ticketTypes.map((ticketType: any) => {
          // Loại bỏ imagesMediaTypes nếu có
          const { imagesMediaTypes, ...ticketTypeWithoutImages } = ticketType;
          return ticketTypeWithoutImages;
        });

        // Cập nhật ticket types trong advanced info
        existingAdvancedInfo.ticketTypes = processedTicketTypes;
        existingAdvancedInfo.updatedAt = Date.now();

        await this.productAdvancedInfoRepository.save(existingAdvancedInfo);
        this.logger.log(`Successfully updated ticket types for EVENT product ${product.id}`);
      }
    } catch (error) {
      this.logger.error(`Error updating ticket types for EVENT product ${product.id}: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_UPDATE_FAILED,
        `Lỗi khi cập nhật ticket types: ${error.message}`,
      );
    }
  }


}
