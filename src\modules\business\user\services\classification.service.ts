import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import {
  UserClassificationRepository,

  CustomFieldRepository,
  UserProductRepository,
} from '@modules/business/repositories';
import {
  CreateClassificationDto,
  UpdateClassificationDto,
  ClassificationResponseDto,
  CustomFieldInputDto,
} from '../dto';
import { UserClassification } from '@modules/business/entities';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { PriceTypeEnum } from '@modules/business/enums';
import { MetadataHelper } from '../helpers/metadata.helper';
import { S3Service } from '@shared/services/s3.service';
import { CdnService } from '@shared/services/cdn.service';
import { generateS3Key, CategoryFolderEnum } from '@shared/utils/generators/s3-key-generator.util';
import { ImageTypeEnum } from '@shared/utils/file/image-media_type.util';
import { TimeIntervalEnum } from '@shared/utils/time/time-interval.util';
import { FileSizeEnum } from '@shared/utils/file/file-size.util';

/**
 * Service xử lý logic nghiệp vụ cho phân loại sản phẩm
 */
@Injectable()
export class ClassificationService {
  private readonly logger = new Logger(ClassificationService.name);

  constructor(
    private readonly userClassificationRepository: UserClassificationRepository,
    private readonly customFieldRepository: CustomFieldRepository,
    private readonly userProductRepository: UserProductRepository,
    private readonly metadataHelper: MetadataHelper,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Tạo phân loại mới cho sản phẩm
   * @param productId ID của sản phẩm
   * @param createDto DTO chứa thông tin phân loại
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin phân loại đã tạo
   */
  @Transactional()
  async create(
    productId: number,
    createDto: CreateClassificationDto,
    userId: number,
  ): Promise<ClassificationResponseDto> {
    try {
      this.logger.log(`Tạo phân loại mới cho sản phẩm ${productId}, userId=${userId}`);

      // Xử lý custom fields nếu có (classification custom fields không cần validate với database)
      if (createDto.customFields && createDto.customFields.length > 0) {
        this.logger.log(`Xử lý ${createDto.customFields.length} custom fields cho classification`);
      }

      // Lấy thông tin sản phẩm để kiểm tra loại giá và quyền sở hữu
      const product = await this.userProductRepository.findById(productId);
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm với ID ${productId}`,
        );
      }

      // Kiểm tra quyền sở hữu
      if (product.createdBy !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_UNAUTHORIZED,
          `Bạn không có quyền tạo phân loại cho sản phẩm này`,
        );
      }

      // Kiểm tra giá sản phẩm theo loại giá
      this.validateClassificationPrice(createDto.price, product.typePrice, createDto.type);

      // Kiểm tra xem có classification cũ cùng type không để preserve images TRƯỚC khi tạo classification mới
      let existingImages: any[] = [];
      let existingClassificationsToDelete: any[] = [];
      try {
        const existingClassifications = await this.userClassificationRepository.find({
          where: { productId, type: createDto.type }
        });

        if (existingClassifications.length > 0) {
          const latestClassification = existingClassifications[existingClassifications.length - 1];
          if (latestClassification.metadata?.images) {
            existingImages = latestClassification.metadata.images;
            this.logger.log(`Found ${existingImages.length} existing images from previous classification`);
          }

          // Lưu danh sách classifications cũ để xóa sau khi tạo mới thành công
          existingClassificationsToDelete = existingClassifications;
          this.logger.log(`Found ${existingClassifications.length} existing classifications to replace`);
        }
      } catch (error) {
        this.logger.warn(`Could not fetch existing classifications: ${error.message}`);
      }

      // Tạo metadata cho classification (không cần validate với database vì là custom fields tự tạo)
      const metadata = this.metadataHelper.buildClassificationMetadata(
        createDto.customFields || [],
        [], // Không lưu imagesMediaTypes vào metadata
        [] // Chưa có ảnh thực tế, sẽ được cập nhật sau khi có classification ID
      );

      // Validate số lượng mua nếu có
      if (createDto.minQuantityPerPurchase !== undefined || createDto.maxQuantityPerPurchase !== undefined) {
        this.validatePurchaseQuantity(createDto.minQuantityPerPurchase, createDto.maxQuantityPerPurchase);
      }

      // Tạo phân loại mới
      const classification = new UserClassification();
      classification.type = createDto.type;
      classification.description = createDto.description || null;
      classification.price = createDto.price;
      classification.productId = productId;
      classification.metadata = metadata;
      classification.sku = createDto.sku || null;
      classification.minQuantityPerPurchase = createDto.minQuantityPerPurchase || null;
      classification.maxQuantityPerPurchase = createDto.maxQuantityPerPurchase || null;

      // Lưu phân loại vào database
      const savedClassification = await this.userClassificationRepository.save(classification);

      // Cập nhật metadata với key ảnh nếu có imageOperations hoặc imagesMediaTypes
      let finalImages = [...existingImages]; // Bắt đầu với images cũ

      // Xử lý imageOperations (format mới với operations)
      if (createDto.imageOperations && createDto.imageOperations.length > 0) {
        // Xử lý DELETE operations trước
        const deleteOperations = createDto.imageOperations.filter(op => op.operation === 'DELETE');
        for (const deleteOp of deleteOperations) {
          if (deleteOp.key) {
            finalImages = finalImages.filter(img => img.key !== deleteOp.key);
            this.logger.log(`Deleted image with key: ${deleteOp.key}`);
          } else if (deleteOp.position !== undefined) {
            finalImages = finalImages.filter(img => img.position !== deleteOp.position);
            this.logger.log(`Deleted image at position: ${deleteOp.position}`);
          }
        }

        // Lọc ra các thao tác ADD để tạo key ảnh
        const addOperations = createDto.imageOperations.filter(op => op.operation === 'ADD');

        if (addOperations.length > 0) {
          // Tạo key ảnh với classification ID thực tế
          const now = Date.now();
          const imageKeys = addOperations.map((_, index) => {
            const fileName = `classification-${savedClassification.id}-image-${index}-${now}`;
            return generateS3Key({
              baseFolder: 'business',
              categoryFolder: CategoryFolderEnum.IMAGE,
              fileName: fileName,
              useTimeFolder: true,
            });
          });

          // Tạo thông tin ảnh với key, position dựa trên finalImages
          const images = imageKeys.map((key, index) => ({
            key: key,
            position: finalImages.length + index
          }));

          // Thêm images mới vào finalImages
          finalImages = [...finalImages, ...images];
        }
      }
      // Xử lý imagesMediaTypes (format cũ - mảng string đơn giản cho API tạo mới)
      else if (createDto.imagesMediaTypes && createDto.imagesMediaTypes.length > 0) {
        // Kiểm tra xem imagesMediaTypes là mảng string hay mảng operations
        const firstItem = createDto.imagesMediaTypes[0];

        if (typeof firstItem === 'string') {
          // Format cũ: mảng string đơn giản ["image/jpeg", "image/png"]
          const now = Date.now();
          const imageKeys = createDto.imagesMediaTypes.map((_, index) => {
            const fileName = `classification-${savedClassification.id}-image-${index}-${now}`;
            return generateS3Key({
              baseFolder: 'business',
              categoryFolder: CategoryFolderEnum.IMAGE,
              fileName: fileName,
              useTimeFolder: true,
            });
          });

          // Tạo thông tin ảnh với key, position dựa trên finalImages
          const images = imageKeys.map((key, index) => ({
            key: key,
            position: finalImages.length + index
          }));

          // Thêm images mới vào finalImages
          finalImages = [...finalImages, ...images];
        } else {
          // Format mới: mảng operations (fallback cho tương thích ngược)
          const imageOperations = createDto.imagesMediaTypes as any[];

          // Xử lý DELETE operations trước
          const deleteOperations = imageOperations.filter(op => op.operation === 'DELETE');
          for (const deleteOp of deleteOperations) {
            if (deleteOp.key) {
              finalImages = finalImages.filter(img => img.key !== deleteOp.key);
              this.logger.log(`Deleted image with key: ${deleteOp.key}`);
            } else if (deleteOp.position !== undefined) {
              finalImages = finalImages.filter(img => img.position !== deleteOp.position);
              this.logger.log(`Deleted image at position: ${deleteOp.position}`);
            }
          }

          // Lọc ra các thao tác ADD để tạo key ảnh
          const addOperations = imageOperations.filter(op => op.operation === 'ADD');

          if (addOperations.length > 0) {
            // Tạo key ảnh với classification ID thực tế
            const now = Date.now();
            const imageKeys = addOperations.map((_, index) => {
              const fileName = `classification-${savedClassification.id}-image-${index}-${now}`;
              return generateS3Key({
                baseFolder: 'business',
                categoryFolder: CategoryFolderEnum.IMAGE,
                fileName: fileName,
                useTimeFolder: true,
              });
            });

            // Tạo thông tin ảnh với key, position dựa trên finalImages
            const images = imageKeys.map((key, index) => ({
              key: key,
              position: finalImages.length + index
            }));

            // Thêm images mới vào finalImages
            finalImages = [...finalImages, ...images];
          }
        }
      }

      // Luôn cập nhật metadata với finalImages (bao gồm cả trường hợp có hoặc không có operations)
      const updatedMetadata = this.metadataHelper.buildClassificationMetadata(
        createDto.customFields || [],
        [], // Không lưu imagesMediaTypes vào metadata
        finalImages // Sử dụng finalImages sau khi đã xử lý DELETE và ADD
      );

      savedClassification.metadata = updatedMetadata;
      this.logger.log(`Saving classification metadata with ${finalImages.length} images`);
      await this.userClassificationRepository.save(savedClassification);

      // Xóa các classifications cũ sau khi tạo mới thành công
      if (existingClassificationsToDelete.length > 0) {
        for (const oldClassification of existingClassificationsToDelete) {
          await this.userClassificationRepository.delete(oldClassification.id);
          this.logger.log(`Deleted old classification with ID: ${oldClassification.id}`);
        }
      }

      // Lấy custom fields từ metadata (classification custom fields sử dụng customFieldId)
      const customFieldsFromMetadata = savedClassification.metadata?.customFields || [];
      const customFieldsResponse = customFieldsFromMetadata.map(cf => ({
        customFieldId: cf.customFieldId,
        value: cf.value,
      }));

      // Đảm bảo price có đầy đủ thông tin
      let price = savedClassification.price;
      if (price && typeof price === 'object') {
        // Đảm bảo có đầy đủ các trường listPrice, salePrice, value, currency
        if (price.listPrice === undefined && price.value !== undefined) {
          price.listPrice = price.value;
        }
        if (price.salePrice === undefined && price.value !== undefined) {
          price.salePrice = price.value;
        }
        if (price.value === undefined && price.salePrice !== undefined) {
          price.value = price.salePrice;
        }
      }

      // Tạo upload URLs cho ảnh classification
      let classificationUploadUrls: any[] = [];
      const imagesFromMetadataForUpload = savedClassification.metadata?.images || [];

      // Tìm các ảnh mới được thêm bằng cách so sánh với existing images
      const existingImageKeys = existingImages.map(img => img.key);
      const newImages = imagesFromMetadataForUpload.filter(img => !existingImageKeys.includes(img.key));

      if (newImages.length > 0) {
        // Xử lý imageOperations (format mới)
        if (createDto.imageOperations && createDto.imageOperations.length > 0) {
          const addOperations = createDto.imageOperations.filter(op => op.operation === 'ADD');

          for (let i = 0; i < addOperations.length && i < newImages.length; i++) {
            try {
              const addOperation = addOperations[i];
              const mediaType = addOperation.mimeType as ImageTypeEnum;
              const imageKey = newImages[i].key;

              // Tạo presigned URL với key từ metadata
              const url = await this.s3Service.createPresignedWithID(
                imageKey,
                TimeIntervalEnum.FIFTEEN_MINUTES,
                mediaType,
                FileSizeEnum.FIVE_MB,
              );

              this.logger.debug(`Created presigned URL for classification image upload: ${imageKey} with position ${newImages[i].position}`);

              classificationUploadUrls.push({
                url: url,
                key: imageKey,
                index: newImages[i].position || i
              });
            } catch (error) {
              this.logger.error(`Lỗi khi tạo presigned URL cho ảnh classification: ${error.message}`, error.stack);
            }
          }
        }
        // Xử lý imagesMediaTypes (format cũ - mảng string)
        else if (createDto.imagesMediaTypes && createDto.imagesMediaTypes.length > 0) {
          const firstItem = createDto.imagesMediaTypes[0];

          if (typeof firstItem === 'string') {
            // Format cũ: mảng string đơn giản
            for (let i = 0; i < createDto.imagesMediaTypes.length && i < newImages.length; i++) {
              try {
                const mediaType = createDto.imagesMediaTypes[i] as ImageTypeEnum;
                const imageKey = newImages[i].key;

                // Tạo presigned URL với key từ metadata
                const url = await this.s3Service.createPresignedWithID(
                  imageKey,
                  TimeIntervalEnum.FIFTEEN_MINUTES,
                  mediaType,
                  FileSizeEnum.FIVE_MB,
                );

                this.logger.debug(`Created presigned URL for classification image upload: ${imageKey} with position ${newImages[i].position}`);

                classificationUploadUrls.push({
                  url: url,
                  key: imageKey,
                  index: newImages[i].position || i
                });
              } catch (error) {
                this.logger.error(`Lỗi khi tạo presigned URL cho ảnh classification: ${error.message}`, error.stack);
              }
            }
          } else {
            // Format mới: mảng operations (fallback)
            const imageOperations = createDto.imagesMediaTypes as any[];
            const addOperations = imageOperations.filter(op => op.operation === 'ADD');

            for (let i = 0; i < addOperations.length && i < newImages.length; i++) {
              try {
                const addOperation = addOperations[i];
                const mediaType = addOperation.mimeType as ImageTypeEnum;
                const imageKey = newImages[i].key;

                // Tạo presigned URL với key từ metadata
                const url = await this.s3Service.createPresignedWithID(
                  imageKey,
                  TimeIntervalEnum.FIFTEEN_MINUTES,
                  mediaType,
                  FileSizeEnum.FIVE_MB,
                );

                this.logger.debug(`Created presigned URL for classification image upload: ${imageKey} with position ${newImages[i].position}`);

                classificationUploadUrls.push({
                  url: url,
                  key: imageKey,
                  index: newImages[i].position || i
                });
              } catch (error) {
                this.logger.error(`Lỗi khi tạo presigned URL cho ảnh classification: ${error.message}`, error.stack);
              }
            }
          }
        }
      }

      // Lấy ảnh thực tế từ metadata và tạo URL CDN
      const imagesFromMetadata = savedClassification.metadata?.images || [];
      const imagesWithUrls = await this.generateImageUrls(imagesFromMetadata);

      // Trả về kết quả
      const result: any = {
        id: savedClassification.id,
        type: savedClassification.type,
        description: savedClassification.description,
        price,
        customFields: customFieldsResponse,
        sku: savedClassification.sku,
        minQuantityPerPurchase: savedClassification.minQuantityPerPurchase,
        maxQuantityPerPurchase: savedClassification.maxQuantityPerPurchase,
        // Không trả về imagesMediaTypes và imageOperations trong response
        imagesMediaTypes: imagesWithUrls, // Ảnh thực tế với URL CDN (lúc tạo sẽ rỗng)
      };

      // Thêm uploadUrls nếu có
      if (classificationUploadUrls.length > 0) {
        result.uploadUrls = {
          classificationId: savedClassification.id,
          imagesUploadUrls: classificationUploadUrls
        };
      }

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo phân loại: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.CLASSIFICATION_CREATION_FAILED,
        `Lỗi khi tạo phân loại: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật phân loại
   * @param id ID của phân loại
   * @param updateDto DTO chứa thông tin cập nhật
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin phân loại đã cập nhật
   */
  @Transactional()
  async update(
    id: number,
    updateDto: UpdateClassificationDto,
    userId: number,
  ): Promise<ClassificationResponseDto> {
    try {
      this.logger.log(`Cập nhật phân loại với ID ${id}, userId=${userId}`);

      // Tìm phân loại theo ID
      const classification = await this.userClassificationRepository.findById(id);
      if (!classification) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CLASSIFICATION_NOT_FOUND,
          `Không tìm thấy phân loại với ID ${id}`,
        );
      }

      // Lấy thông tin sản phẩm để kiểm tra quyền sở hữu
      const product = await this.userProductRepository.findById(classification.productId);
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm với ID ${classification.productId}`,
        );
      }

      // Kiểm tra quyền sở hữu
      if (product.createdBy !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_UNAUTHORIZED,
          `Bạn không có quyền cập nhật phân loại này`,
        );
      }

      // Cập nhật các trường được cung cấp
      if (updateDto.type !== undefined) {
        classification.type = updateDto.type;
      }
      if (updateDto.description !== undefined) {
        classification.description = updateDto.description;
      }
      if (updateDto.price !== undefined) {
        // Kiểm tra giá sản phẩm theo loại giá
        this.validateClassificationPrice(updateDto.price, product.typePrice, classification.type);
        classification.price = updateDto.price;
      }
      if (updateDto.sku !== undefined) {
        classification.sku = updateDto.sku;
      }
      if (updateDto.minQuantityPerPurchase !== undefined) {
        classification.minQuantityPerPurchase = updateDto.minQuantityPerPurchase;
      }
      if (updateDto.maxQuantityPerPurchase !== undefined) {
        classification.maxQuantityPerPurchase = updateDto.maxQuantityPerPurchase;
      }

      // Validate số lượng mua nếu có thay đổi
      if (updateDto.minQuantityPerPurchase !== undefined || updateDto.maxQuantityPerPurchase !== undefined) {
        const finalMinQuantity = updateDto.minQuantityPerPurchase !== undefined
          ? updateDto.minQuantityPerPurchase
          : classification.minQuantityPerPurchase;
        const finalMaxQuantity = updateDto.maxQuantityPerPurchase !== undefined
          ? updateDto.maxQuantityPerPurchase
          : classification.maxQuantityPerPurchase;
        this.validatePurchaseQuantity(finalMinQuantity, finalMaxQuantity);
      }

      // Xử lý các trường tùy chỉnh và imageOperations/imagesMediaTypes nếu có
      if (updateDto.customFields !== undefined || updateDto.imageOperations !== undefined || updateDto.imagesMediaTypes !== undefined) {
        // Lấy metadata hiện tại
        const currentMetadata = classification.metadata || { customFields: [] };

        // Sử dụng customFields từ updateDto nếu có, nếu không giữ nguyên
        const customFields = updateDto.customFields !== undefined
          ? updateDto.customFields
          : currentMetadata.customFields || [];

        // Không cần lấy imagesMediaTypes từ metadata nữa vì không lưu vào đó

        // Xử lý ảnh: xử lý các thao tác ADD và DELETE
        let images = currentMetadata.images || [];
        const imageOperationsToProcess = updateDto.imageOperations || updateDto.imagesMediaTypes;
        if (imageOperationsToProcess !== undefined) {
          // Kiểm tra xem có phải là mảng operations không (chỉ xử lý format mới trong update)
          const firstItem = imageOperationsToProcess[0];
          if (firstItem && typeof firstItem === 'object' && 'operation' in firstItem) {
            // Format mới: mảng operations
            const operations = imageOperationsToProcess as Array<{
              operation: 'ADD' | 'DELETE';
              position?: number;
              key?: string;
              mimeType?: string;
            }>;

            // Xử lý các thao tác DELETE trước
            const deleteOperations = operations.filter(op => op.operation === 'DELETE');
            for (const deleteOp of deleteOperations) {
              if (deleteOp.key) {
                // Xóa theo key
                images = images.filter(img => img.key !== deleteOp.key);
              } else if (deleteOp.position !== undefined) {
                // Xóa theo position
                images = images.filter(img => img.position !== deleteOp.position);
              }
            }

            // Xử lý các thao tác ADD
            const addOperations = operations.filter(op => op.operation === 'ADD');
            if (addOperations.length > 0) {
              const now = Date.now();
              const newImages = addOperations.map((_, index) => {
                const fileName = `classification-${classification.id}-image-${images.length + index}-${now}`;
                return {
                  key: generateS3Key({
                    baseFolder: 'business',
                    categoryFolder: CategoryFolderEnum.IMAGE,
                    fileName: fileName,
                    useTimeFolder: true,
                  }),
                  position: images.length + index
                };
              });
              images = [...images, ...newImages];
            }
          }
          // Nếu không phải format operations, bỏ qua (format cũ không hỗ trợ trong update)
        }

        // Cập nhật metadata với classification custom fields và ảnh (không lưu imagesMediaTypes)
        const newMetadata = this.metadataHelper.buildClassificationMetadata(
          customFields,
          [], // Không lưu imagesMediaTypes vào metadata
          images // Ảnh mới hoặc ảnh cũ
        );
        classification.metadata = newMetadata;
      }

      // Lưu phân loại vào database
      const updatedClassification = await this.userClassificationRepository.save(classification);

      // Lấy custom fields từ metadata (classification custom fields không có ID)
      const customFieldsFromMetadata = updatedClassification.metadata?.customFields || [];
      const customFieldsResponse = customFieldsFromMetadata.map(cf => ({
        label: cf.label,
        type: cf.type,
        required: cf.required || false,
        value: cf.value,
      }));

      // Đảm bảo price có đầy đủ thông tin
      let price = updatedClassification.price;
      if (price && typeof price === 'object') {
        // Đảm bảo có đầy đủ các trường listPrice, salePrice, value, currency
        if (price.listPrice === undefined && price.value !== undefined) {
          price.listPrice = price.value;
        }
        if (price.salePrice === undefined && price.value !== undefined) {
          price.salePrice = price.value;
        }
        if (price.value === undefined && price.salePrice !== undefined) {
          price.value = price.salePrice;
        }
      }

      // Không lấy imagesMediaTypes từ metadata nữa vì không lưu vào đó

      // Tạo upload URLs cho ảnh classification nếu có thao tác ADD (sử dụng key từ metadata)
      let classificationUploadUrls: any[] = [];
      const imageOperationsForUploadUpdate = updateDto.imageOperations || updateDto.imagesMediaTypes;
      if (imageOperationsForUploadUpdate && imageOperationsForUploadUpdate.length > 0) {
        const imagesFromMetadata = updatedClassification.metadata?.images || [];

        // Kiểm tra xem có phải là mảng operations không
        const firstItem = imageOperationsForUploadUpdate[0];
        if (firstItem && typeof firstItem === 'object' && 'operation' in firstItem) {
          const operations = imageOperationsForUploadUpdate as Array<{
            operation: 'ADD' | 'DELETE';
            position?: number;
            key?: string;
            mimeType?: string;
          }>;
          const addOperations = operations.filter(op => op.operation === 'ADD');

          // Tìm các ảnh mới được thêm (những ảnh có position cao nhất)
          if (addOperations.length > 0) {
            // Sắp xếp images theo position để tìm các ảnh mới
            const sortedImages = [...imagesFromMetadata].sort((a, b) => (b.position || 0) - (a.position || 0));
            const newImages = sortedImages.slice(0, addOperations.length);

            for (let i = 0; i < addOperations.length && i < newImages.length; i++) {
              try {
                const addOperation = addOperations[i];
                const mediaType = addOperation.mimeType as ImageTypeEnum;
                const imageKey = newImages[i].key;

                // Tạo presigned URL với key từ metadata
                const url = await this.s3Service.createPresignedWithID(
                  imageKey,
                  TimeIntervalEnum.FIFTEEN_MINUTES,
                  mediaType,
                  FileSizeEnum.FIVE_MB,
                );

                this.logger.debug(`Created presigned URL for classification image upload: ${imageKey} with position ${newImages[i].position}`);

                classificationUploadUrls.push({
                  url: url,
                  key: imageKey,
                  index: newImages[i].position || i
                });
              } catch (error) {
                this.logger.error(`Lỗi khi tạo presigned URL cho ảnh classification: ${error.message}`, error.stack);
              }
            }
          }
        }
        // Nếu không phải format operations, bỏ qua
      }

      // Lấy ảnh thực tế từ metadata và tạo URL CDN
      const imagesFromMetadata = updatedClassification.metadata?.images || [];
      const imagesWithUrls = await this.generateImageUrls(imagesFromMetadata);

      // Trả về kết quả
      return {
        id: updatedClassification.id,
        type: updatedClassification.type,
        description: updatedClassification.description,
        price,
        customFields: customFieldsResponse,
        sku: updatedClassification.sku,
        minQuantityPerPurchase: updatedClassification.minQuantityPerPurchase,
        maxQuantityPerPurchase: updatedClassification.maxQuantityPerPurchase,
        // Không trả về imagesMediaTypes và imageOperations trong response
        imagesMediaTypes: imagesWithUrls, // Ảnh thực tế với URL CDN
        uploadUrls: classificationUploadUrls.length > 0 ? {
          classificationId: updatedClassification.id,
          imagesUploadUrls: classificationUploadUrls
        } : undefined,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật phân loại: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.CLASSIFICATION_UPDATE_FAILED,
        `Lỗi khi cập nhật phân loại: ${error.message}`,
      );
    }
  }

  /**
   * Xóa phân loại
   * @param id ID của phân loại
   * @param userId ID của người dùng hiện tại
   */
  @Transactional()
  async delete(id: number, userId: number): Promise<void> {
    try {
      this.logger.log(`Xóa phân loại với ID ${id}, userId=${userId}`);

      // Tìm phân loại theo ID
      const classification = await this.userClassificationRepository.findById(id);
      if (!classification) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CLASSIFICATION_NOT_FOUND,
          `Không tìm thấy phân loại với ID ${id}`,
        );
      }

      // Lấy thông tin sản phẩm để kiểm tra quyền sở hữu
      const product = await this.userProductRepository.findById(classification.productId);
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm với ID ${classification.productId}`,
        );
      }

      // Kiểm tra quyền sở hữu
      if (product.createdBy !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_UNAUTHORIZED,
          `Bạn không có quyền xóa phân loại này`,
        );
      }

      // Metadata sẽ được xóa cùng với classification, không cần xử lý riêng

      // Xóa phân loại
      await this.userClassificationRepository.delete(id);
    } catch (error) {
      this.logger.error(`Lỗi khi xóa phân loại: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.CLASSIFICATION_DELETION_FAILED,
        `Lỗi khi xóa phân loại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách phân loại theo ID sản phẩm
   * @param productId ID của sản phẩm
   * @returns Danh sách phân loại
   */
  async getByProductId(productId: number): Promise<ClassificationResponseDto[]> {
    try {
      this.logger.log(`Lấy danh sách phân loại cho sản phẩm ${productId}`);

      // Tìm tất cả phân loại của sản phẩm
      const classifications = await this.userClassificationRepository.findByProductId_user(productId);

      // Chuyển đổi sang DTO response
      const result: ClassificationResponseDto[] = [];
      for (const classification of classifications) {
        // Lấy custom fields từ metadata (classification custom fields sử dụng customFieldId)
        const customFieldsFromMetadata = classification.metadata?.customFields || [];
        const customFields: CustomFieldInputDto[] = customFieldsFromMetadata.map(cf => ({
          customFieldId: cf.customFieldId,
          value: cf.value,
        }));

        // Đảm bảo price có đầy đủ thông tin
        let price = classification.price;
        if (price && typeof price === 'object') {
          // Đảm bảo có đầy đủ các trường listPrice, salePrice, value, currency
          if (price.listPrice === undefined && price.value !== undefined) {
            price.listPrice = price.value;
          }
          if (price.salePrice === undefined && price.value !== undefined) {
            price.salePrice = price.value;
          }
          if (price.value === undefined && price.salePrice !== undefined) {
            price.value = price.salePrice;
          }
        }

        // Lấy ảnh thực tế từ metadata (không có imagesMediaTypes nữa)
        const imagesFromMetadata = classification.metadata?.images || [];
        const imagesWithUrls = await this.generateImageUrls(imagesFromMetadata);

        // Thêm vào kết quả
        result.push({
          id: classification.id,
          type: classification.type,
          description: classification.description,
          price,
          customFields,
          sku: classification.sku,
          minQuantityPerPurchase: classification.minQuantityPerPurchase,
          maxQuantityPerPurchase: classification.maxQuantityPerPurchase,
          imagesMediaTypes: imagesWithUrls, // Ảnh thực tế với URL CDN
        });
      }

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách phân loại: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.CLASSIFICATION_FIND_FAILED,
        `Lỗi khi lấy danh sách phân loại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết phân loại theo ID
   * @param id ID của phân loại
   * @returns Chi tiết phân loại
   */
  async getById(id: number): Promise<ClassificationResponseDto> {
    try {
      this.logger.log(`Lấy chi tiết phân loại với ID ${id}`);

      // Tìm phân loại theo ID
      const classification = await this.userClassificationRepository.findById(id);
      if (!classification) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CLASSIFICATION_NOT_FOUND,
          `Không tìm thấy phân loại với ID ${id}`,
        );
      }

      // Lấy custom fields từ metadata (classification custom fields sử dụng customFieldId)
      const customFieldsFromMetadata = classification.metadata?.customFields || [];
      const customFields: CustomFieldInputDto[] = customFieldsFromMetadata.map(cf => ({
        customFieldId: cf.customFieldId,
        value: cf.value,
      }));

      // Đảm bảo price có đầy đủ thông tin
      let price = classification.price;
      if (price && typeof price === 'object') {
        // Đảm bảo có đầy đủ các trường listPrice, salePrice, value, currency
        if (price.listPrice === undefined && price.value !== undefined) {
          price.listPrice = price.value;
        }
        if (price.salePrice === undefined && price.value !== undefined) {
          price.salePrice = price.value;
        }
        if (price.value === undefined && price.salePrice !== undefined) {
          price.value = price.salePrice;
        }
      }

      // Lấy ảnh thực tế từ metadata (không có imagesMediaTypes nữa)
      const imagesFromMetadata = classification.metadata?.images || [];
      const imagesWithUrls = await this.generateImageUrls(imagesFromMetadata);

      // Trả về kết quả
      return {
        id: classification.id,
        type: classification.type,
        description: classification.description,
        price,
        customFields,
        sku: classification.sku,
        minQuantityPerPurchase: classification.minQuantityPerPurchase,
        maxQuantityPerPurchase: classification.maxQuantityPerPurchase,
        imagesMediaTypes: imagesWithUrls, // Ảnh thực tế với URL CDN
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy chi tiết phân loại: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.CLASSIFICATION_FIND_FAILED,
        `Lỗi khi lấy chi tiết phân loại: ${error.message}`,
      );
    }
  }

  /**
   * Kiểm tra giá sản phẩm theo loại giá
   * @param price Giá của phân loại
   * @param typePrice Loại giá của sản phẩm
   * @param classificationType Loại phân loại
   * @throws AppException nếu giá không hợp lệ
   */
  private validateClassificationPrice(price: any, typePrice: PriceTypeEnum, classificationType: string): void {
    switch (typePrice) {
      case PriceTypeEnum.HAS_PRICE:
        // Kiểm tra có đủ các trường cần thiết không
        if (!price || !price.listPrice || !price.salePrice || !price.currency) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CLASSIFICATION_VALIDATION_FAILED,
            `Phân loại "${classificationType}" phải có đầy đủ listPrice, salePrice và currency khi sản phẩm có loại giá HAS_PRICE`,
          );
        }

        // Kiểm tra giá bán phải nhỏ hơn hoặc bằng giá niêm yết
        if (price.salePrice > price.listPrice) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CLASSIFICATION_VALIDATION_FAILED,
            `Giá bán (salePrice) phải nhỏ hơn hoặc bằng giá niêm yết (listPrice) trong phân loại "${classificationType}"`,
          );
        }
        break;

      case PriceTypeEnum.STRING_PRICE:
        // Kiểm tra có trường priceDescription không
        if (!price || !price.priceDescription) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CLASSIFICATION_VALIDATION_FAILED,
            `Phân loại "${classificationType}" phải có priceDescription khi sản phẩm có loại giá STRING_PRICE`,
          );
        }
        break;

      case PriceTypeEnum.NO_PRICE:
        // Kiểm tra price phải là null
        if (price !== null) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CLASSIFICATION_VALIDATION_FAILED,
            `Phân loại "${classificationType}" không được có giá khi sản phẩm có loại giá NO_PRICE`,
          );
        }
        break;

      // Các loại giá khác như HOURLY, DAILY, MONTHLY, YEARLY, CONTACT
      default:
        // Kiểm tra có đủ các trường cần thiết không
        if (!price || !price.value || !price.currency) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CLASSIFICATION_VALIDATION_FAILED,
            `Phân loại "${classificationType}" phải có đầy đủ value và currency khi sản phẩm có loại giá ${typePrice}`,
          );
        }
        break;
    }
  }

  /**
   * Tạo presigned URLs cho việc tải lên ảnh classification
   * @param classificationId ID của classification
   * @param imageOperations Danh sách thao tác ảnh (chỉ ADD operations)
   * @returns Danh sách upload URLs
   */
  private async createClassificationImageUploadUrls(
    classificationId: number,
    imageOperations: Array<{operation: 'ADD' | 'DELETE'; mimeType?: string; key?: string; position?: number}>
  ): Promise<any[]> {
    const uploadUrls: any[] = [];
    const now = Date.now();
    const addOperations = imageOperations.filter(op => op.operation === 'ADD');

    for (let i = 0; i < addOperations.length; i++) {
      try {
        const addOperation = addOperations[i];
        const mediaType = addOperation.mimeType as ImageTypeEnum;

        // Tạo tên file với classification ID và timestamp
        const fileName = `classification-${classificationId}-image-${i}-${now}`;

        // Tạo S3 key cho ảnh classification
        const key = generateS3Key({
          baseFolder: 'business',
          categoryFolder: CategoryFolderEnum.IMAGE,
          fileName: fileName,
          useTimeFolder: true,
        });

        // Tạo presigned URL
        const url = await this.s3Service.createPresignedWithID(
          key,
          TimeIntervalEnum.FIFTEEN_MINUTES,
          mediaType,
          FileSizeEnum.FIVE_MB,
        );

        this.logger.debug(`Created presigned URL for classification image upload: ${key} with position ${i}`);

        uploadUrls.push({
          url: url,
          key: key,
          index: i
        });
      } catch (error) {
        this.logger.error(`Lỗi khi tạo upload URL cho ảnh classification: ${error.message}`, error.stack);
        // Tiếp tục với ảnh tiếp theo thay vì dừng toàn bộ
      }
    }

    return uploadUrls;
  }

  /**
   * Tạo URL ảnh với CDN cho danh sách ảnh
   * @param images Danh sách ảnh từ metadata
   * @returns Danh sách ảnh với URL CDN
   */
  private async generateImageUrls(images: Array<{key: string, size?: number, position?: number}>): Promise<Array<{key: string, position: number, url: string}>> {
    if (!images || images.length === 0) {
      return [];
    }

    return Promise.all(
      images.map(async (image) => {
        try {
          // Tạo CDN signed URL
          const url = this.cdnService.generateUrlView(image.key, TimeIntervalEnum.ONE_HOUR);
          const timestamp = Date.now();

          return {
            key: image.key,
            position: image.position || 0,
            url: url ? `${url}?t=${timestamp}` : ''
          };
        } catch (error) {
          this.logger.error(`Lỗi khi tạo URL cho ảnh classification: ${error.message}`, error.stack);
          return {
            key: image.key,
            position: image.position || 0,
            url: ''
          };
        }
      })
    );
  }

  /**
   * Cập nhật thông tin ảnh thực tế cho classification sau khi upload
   * @param classificationId ID của classification
   * @param userId ID của user
   * @param images Danh sách ảnh đã upload
   * @returns Classification đã cập nhật
   */
  @Transactional()
  async updateClassificationImages(
    classificationId: number,
    userId: number,
    images: Array<{key: string, size: number, position: number}>
  ): Promise<ClassificationResponseDto> {
    try {
      this.logger.log(`Cập nhật ảnh cho classification ${classificationId}, userId=${userId}`);

      // Kiểm tra classification có tồn tại và thuộc về user không
      const classification = await this.userClassificationRepository
        .createQueryBuilder('classification')
        .leftJoinAndSelect('classification.product', 'product')
        .where('classification.id = :classificationId', { classificationId })
        .andWhere('product.createdBy = :userId', { userId })
        .getOne();

      if (!classification) {
        throw new AppException(BUSINESS_ERROR_CODES.CLASSIFICATION_NOT_FOUND);
      }

      // Lấy metadata hiện tại
      const currentMetadata = classification.metadata || { customFields: [] };

      // Cập nhật metadata với thông tin ảnh thực tế (không lưu imagesMediaTypes)
      const newMetadata = this.metadataHelper.buildClassificationMetadata(
        currentMetadata.customFields || [],
        [], // Không lưu imagesMediaTypes vào metadata
        images // Thông tin ảnh thực tế
      );

      // Cập nhật classification
      classification.metadata = newMetadata;
      const updatedClassification = await this.userClassificationRepository.save(classification);

      this.logger.log(`Đã cập nhật ảnh cho classification ${classificationId}`);

      // Xử lý price
      let price = updatedClassification.price;
      if (typeof price === 'string') {
        price = JSON.parse(price);
      }
      if (typeof price === 'object' && price !== null) {
        if (price.salePrice !== undefined && price.salePrice !== null) {
          price.value = price.salePrice;
        }
      }

      // Xử lý custom fields response
      const customFieldsResponse = currentMetadata.customFields || [];

      // Lấy ảnh thực tế từ metadata và tạo URL CDN
      const imagesFromMetadata = updatedClassification.metadata?.images || [];
      const imagesWithUrls = await this.generateImageUrls(imagesFromMetadata);

      // Trả về kết quả
      return {
        id: updatedClassification.id,
        type: updatedClassification.type,
        price,
        customFields: customFieldsResponse,
        imagesMediaTypes: imagesWithUrls, // Ảnh thực tế với URL CDN
      };

    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật ảnh classification: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(BUSINESS_ERROR_CODES.CLASSIFICATION_UPDATE_FAILED);
    }
  }

  /**
   * Validate số lượng mua
   * @param minQuantity Số lượng tối thiểu
   * @param maxQuantity Số lượng tối đa
   * @throws AppException nếu số lượng không hợp lệ
   */
  private validatePurchaseQuantity(
    minQuantity: number | null | undefined,
    maxQuantity: number | null | undefined
  ): void {
    if (minQuantity !== null && minQuantity !== undefined && minQuantity < 1) {
      throw new AppException(
        BUSINESS_ERROR_CODES.CLASSIFICATION_VALIDATION_FAILED,
        'Số lượng tối thiểu mỗi lần mua phải lớn hơn 0',
      );
    }

    if (maxQuantity !== null && maxQuantity !== undefined && maxQuantity < 1) {
      throw new AppException(
        BUSINESS_ERROR_CODES.CLASSIFICATION_VALIDATION_FAILED,
        'Số lượng tối đa mỗi lần mua phải lớn hơn 0',
      );
    }

    if (
      minQuantity !== null && minQuantity !== undefined &&
      maxQuantity !== null && maxQuantity !== undefined &&
      minQuantity > maxQuantity
    ) {
      throw new AppException(
        BUSINESS_ERROR_CODES.CLASSIFICATION_VALIDATION_FAILED,
        'Số lượng tối thiểu không được lớn hơn số lượng tối đa',
      );
    }
  }
}
