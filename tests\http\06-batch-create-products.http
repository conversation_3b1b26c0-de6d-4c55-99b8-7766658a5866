### Test Batch Create Products - Mixed Types
### Tạ<PERSON> nhiều sản phẩm khác loại cùng lúc

POST {{baseUrl}}/user/products/batch
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "products": [
    {
      "name": "Áo polo nam",
      "productType": "PHYSICAL",
      "typePrice": "HAS_PRICE",
      "price": {
        "listPrice": 350000,
        "salePrice": 300000,
        "currency": "VND"
      },
      "description": "Áo polo nam chất liệu cotton cao cấp",
      "imagesMediaTypes": ["image/jpeg"],
      "tags": ["thời trang", "nam", "polo"],
      "shipmentConfig": {
        "widthCm": 25,
        "heightCm": 5,
        "lengthCm": 30,
        "weightGram": 250
      },
      "inventory": {
        "availableQuantity": 50,
        "sku": "POLO-MEN-001",
        "barcode": "1111111111111"
      }
    },
    {
      "name": "<PERSON><PERSON><PERSON><PERSON> học Excel nâng cao",
      "productType": "DIGITAL",
      "typePrice": "HAS_PRICE",
      "price": {
        "listPrice": 600000,
        "salePrice": 500000,
        "currency": "VND"
      },
      "description": "Khóa học Excel từ cơ bản đến nâng cao",
      "imagesMediaTypes": ["image/jpeg"],
      "tags": ["excel", "khóa học", "office"],
      "deliveryMethod": "EMAIL",
      "deliveryTiming": "IMMEDIATE",
      "outputType": "ACCESS_CODE",
      "purchaseCount": 0
    },
    {
      "name": "Workshop thiết kế UI/UX",
      "productType": "EVENT",
      "typePrice": "HAS_PRICE",
      "price": {
        "listPrice": 800000,
        "salePrice": 700000,
        "currency": "VND"
      },
      "description": "Workshop thiết kế UI/UX cho người mới bắt đầu",
      "imagesMediaTypes": ["image/jpeg"],
      "tags": ["workshop", "ui", "ux", "thiết kế"],
      "eventDate": "2024-12-15T09:00:00Z",
      "eventLocation": "Hà Nội",
      "maxAttendees": 30,
      "purchaseCount": 0,
      "ticketTypes": [
        {
          "name": "Vé thường",
          "price": {
            "listPrice": 700000,
            "salePrice": 600000,
            "currency": "VND"
          },
          "description": "Vé tham dự workshop",
          "maxQuantity": 30
        }
      ]
    }
  ]
}

### Test Batch Create Products - All Physical
### Tạo nhiều sản phẩm vật lý cùng lúc

POST {{baseUrl}}/user/products/batch
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "products": [
    {
      "name": "Quần short nam",
      "productType": "PHYSICAL",
      "typePrice": "HAS_PRICE",
      "price": {
        "listPrice": 200000,
        "salePrice": 180000,
        "currency": "VND"
      },
      "description": "Quần short nam thể thao",
      "imagesMediaTypes": ["image/jpeg"],
      "tags": ["quần short", "nam", "thể thao"],
      "shipmentConfig": {
        "widthCm": 20,
        "heightCm": 3,
        "lengthCm": 25,
        "weightGram": 150
      },
      "inventory": {
        "availableQuantity": 100,
        "sku": "SHORT-MEN-001"
      }
    },
    {
      "name": "Áo tank top nữ",
      "productType": "PHYSICAL",
      "typePrice": "HAS_PRICE",
      "price": {
        "listPrice": 150000,
        "salePrice": 130000,
        "currency": "VND"
      },
      "description": "Áo tank top nữ thoáng mát",
      "imagesMediaTypes": ["image/jpeg"],
      "tags": ["áo tank top", "nữ", "thể thao"],
      "shipmentConfig": {
        "widthCm": 20,
        "heightCm": 2,
        "lengthCm": 25,
        "weightGram": 100
      },
      "inventory": {
        "availableQuantity": 80,
        "sku": "TANK-WOMEN-001"
      }
    },
    {
      "name": "Mũ lưỡi trai",
      "productType": "PHYSICAL",
      "typePrice": "HAS_PRICE",
      "price": {
        "listPrice": 120000,
        "salePrice": 100000,
        "currency": "VND"
      },
      "description": "Mũ lưỡi trai unisex",
      "imagesMediaTypes": ["image/jpeg"],
      "tags": ["mũ", "lưỡi trai", "unisex"],
      "shipmentConfig": {
        "widthCm": 25,
        "heightCm": 15,
        "lengthCm": 25,
        "weightGram": 80
      },
      "inventory": {
        "availableQuantity": 60,
        "sku": "CAP-UNISEX-001"
      }
    }
  ]
}

### Test Batch Create Products - All Digital
### Tạo nhiều sản phẩm số cùng lúc

POST {{baseUrl}}/user/products/batch
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "products": [
    {
      "name": "Template PowerPoint",
      "productType": "DIGITAL",
      "typePrice": "HAS_PRICE",
      "price": {
        "listPrice": 300000,
        "salePrice": 250000,
        "currency": "VND"
      },
      "description": "Bộ template PowerPoint chuyên nghiệp",
      "imagesMediaTypes": ["image/jpeg"],
      "tags": ["template", "powerpoint", "presentation"],
      "deliveryMethod": "EMAIL",
      "deliveryTiming": "IMMEDIATE",
      "outputType": "DOWNLOAD_LINK",
      "purchaseCount": 0
    },
    {
      "name": "Plugin WordPress SEO",
      "productType": "DIGITAL",
      "typePrice": "HAS_PRICE",
      "price": {
        "listPrice": 500000,
        "salePrice": 400000,
        "currency": "VND"
      },
      "description": "Plugin WordPress tối ưu SEO",
      "imagesMediaTypes": ["image/jpeg"],
      "tags": ["plugin", "wordpress", "seo"],
      "deliveryMethod": "DASHBOARD",
      "deliveryTiming": "IMMEDIATE",
      "outputType": "DOWNLOAD_LINK",
      "purchaseCount": 0
    },
    {
      "name": "Font chữ Việt Nam",
      "productType": "DIGITAL",
      "typePrice": "HAS_PRICE",
      "price": {
        "listPrice": 200000,
        "salePrice": 150000,
        "currency": "VND"
      },
      "description": "Bộ font chữ Việt Nam đẹp",
      "imagesMediaTypes": ["image/jpeg"],
      "tags": ["font", "việt nam", "typography"],
      "deliveryMethod": "EMAIL",
      "deliveryTiming": "IMMEDIATE",
      "outputType": "DOWNLOAD_LINK",
      "purchaseCount": 0
    }
  ]
}

### Test Batch Create Products - Large Batch (10 products)
### Test với số lượng lớn

POST {{baseUrl}}/user/products/batch
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "products": [
    {
      "name": "Sản phẩm test 1",
      "productType": "PHYSICAL",
      "typePrice": "HAS_PRICE",
      "price": {"listPrice": 100000, "salePrice": 90000, "currency": "VND"},
      "description": "Mô tả sản phẩm test 1",
      "imagesMediaTypes": ["image/jpeg"],
      "tags": ["test"],
      "shipmentConfig": {"widthCm": 10, "heightCm": 10, "lengthCm": 10, "weightGram": 100},
      "inventory": {"availableQuantity": 10, "sku": "TEST-001"}
    },
    {
      "name": "Sản phẩm test 2",
      "productType": "DIGITAL",
      "typePrice": "HAS_PRICE",
      "price": {"listPrice": 200000, "salePrice": 180000, "currency": "VND"},
      "description": "Mô tả sản phẩm test 2",
      "imagesMediaTypes": ["image/jpeg"],
      "tags": ["test"],
      "deliveryMethod": "EMAIL",
      "deliveryTiming": "IMMEDIATE",
      "outputType": "ACCESS_CODE",
      "purchaseCount": 0
    },
    {
      "name": "Sản phẩm test 3",
      "productType": "SERVICE",
      "typePrice": "HAS_PRICE",
      "price": {"listPrice": 300000, "salePrice": 270000, "currency": "VND"},
      "description": "Mô tả sản phẩm test 3",
      "imagesMediaTypes": ["image/jpeg"],
      "tags": ["test"],
      "purchaseCount": 0
    },
    {
      "name": "Sản phẩm test 4",
      "productType": "PHYSICAL",
      "typePrice": "HAS_PRICE",
      "price": {"listPrice": 400000, "salePrice": 360000, "currency": "VND"},
      "description": "Mô tả sản phẩm test 4",
      "imagesMediaTypes": ["image/jpeg"],
      "tags": ["test"],
      "shipmentConfig": {"widthCm": 15, "heightCm": 15, "lengthCm": 15, "weightGram": 200},
      "inventory": {"availableQuantity": 20, "sku": "TEST-004"}
    },
    {
      "name": "Sản phẩm test 5",
      "productType": "DIGITAL",
      "typePrice": "HAS_PRICE",
      "price": {"listPrice": 500000, "salePrice": 450000, "currency": "VND"},
      "description": "Mô tả sản phẩm test 5",
      "imagesMediaTypes": ["image/jpeg"],
      "tags": ["test"],
      "deliveryMethod": "DASHBOARD",
      "deliveryTiming": "IMMEDIATE",
      "outputType": "CONTENT",
      "purchaseCount": 0
    }
  ]
}

### Test Batch Create Products - Error Cases
### Test các trường hợp lỗi

POST {{baseUrl}}/user/products/batch
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "products": [
    {
      "name": "Sản phẩm hợp lệ",
      "productType": "PHYSICAL",
      "typePrice": "HAS_PRICE",
      "price": {"listPrice": 100000, "salePrice": 90000, "currency": "VND"},
      "description": "Sản phẩm này sẽ tạo thành công",
      "imagesMediaTypes": ["image/jpeg"],
      "tags": ["test"],
      "shipmentConfig": {"widthCm": 10, "heightCm": 10, "lengthCm": 10, "weightGram": 100},
      "inventory": {"availableQuantity": 10, "sku": "VALID-001"}
    },
    {
      "name": "",
      "productType": "PHYSICAL",
      "typePrice": "HAS_PRICE",
      "price": {"listPrice": 100000, "salePrice": 90000, "currency": "VND"},
      "description": "Sản phẩm này thiếu tên",
      "imagesMediaTypes": ["image/jpeg"],
      "tags": ["test"],
      "shipmentConfig": {"widthCm": 10, "heightCm": 10, "lengthCm": 10, "weightGram": 100}
    },
    {
      "name": "Sản phẩm thiếu productType",
      "typePrice": "HAS_PRICE",
      "price": {"listPrice": 100000, "salePrice": 90000, "currency": "VND"},
      "description": "Sản phẩm này thiếu productType",
      "imagesMediaTypes": ["image/jpeg"],
      "tags": ["test"]
    }
  ]
}

### Test Batch Create Products - Empty Array
### Test với mảng rỗng

POST {{baseUrl}}/user/products/batch
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "products": []
}

### Test Batch Create Products - Over Limit (3 products for demo)
### Test với nhiều sản phẩm (trong thực tế có thể test với 51 sản phẩm)

POST {{baseUrl}}/user/products/batch
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "products": [
    {"name": "Product 1", "productType": "PHYSICAL", "typePrice": "HAS_PRICE", "price": {"listPrice": 100000, "salePrice": 90000, "currency": "VND"}, "description": "Test product 1", "imagesMediaTypes": ["image/jpeg"], "tags": ["test"], "shipmentConfig": {"widthCm": 10, "heightCm": 10, "lengthCm": 10, "weightGram": 100}, "inventory": {"availableQuantity": 10, "sku": "PROD-001"}},
    {"name": "Product 2", "productType": "DIGITAL", "typePrice": "HAS_PRICE", "price": {"listPrice": 200000, "salePrice": 180000, "currency": "VND"}, "description": "Test product 2", "imagesMediaTypes": ["image/jpeg"], "tags": ["test"], "deliveryMethod": "EMAIL", "deliveryTiming": "IMMEDIATE", "outputType": "ACCESS_CODE", "purchaseCount": 0},
    {"name": "Product 3", "productType": "SERVICE", "typePrice": "HAS_PRICE", "price": {"listPrice": 300000, "salePrice": 270000, "currency": "VND"}, "description": "Test product 3", "imagesMediaTypes": ["image/jpeg"], "tags": ["test"], "purchaseCount": 0}
  ]
}
