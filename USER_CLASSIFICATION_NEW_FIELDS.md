# Cập nhật UserClassification Entity - Thêm các trường mới

## Tóm tắt thay đổi
Đã thêm các trường mới vào UserClassification entity theo cấu trúc database và tạo types tương ứng để đảm bảo type safety.

## Các file đã được tạo/cập nhật

### 1. Entity Update - UserClassification
**File:** `src/modules/business/entities/user-classification.entity.ts`

#### Các trường mới được thêm:
```typescript
// Số lượng tối thiểu mỗi lần mua
minQuantityPerPurchase: number | null;

// Số lượng tối đa mỗi lần mua  
maxQuantityPerPurchase: number | null;

// Mô tả phân loại
description: string | null;

// Trường tùy chỉnh (jsonb)
customFields: ClassificationCustomFields | null;

// Thông tin media hình ảnh (jsonb)
imagesMedia: ClassificationImagesMedia | null;

// Mã SKU của phân loại
sku: string | null;
```

#### Cập nhật metadata type:
```typescript
metadata: UserClassificationMetadata;
```

### 2. Type Definitions - User Classification Types
**File:** `src/modules/business/types/user-classification.type.ts`

#### Interfaces chính:
- **`UserClassificationMetadata`**: Interface cho metadata field
- **`ClassificationCustomField`**: Interface cho custom field trong classification
- **`ClassificationImagesMedia`**: Interface cho images media
- **`ClassificationImage`**: Interface cho từng hình ảnh
- **`ClassificationCustomFields`**: Interface cho custom fields field riêng biệt
- **`ClassificationCustomFieldValue`**: Interface cho giá trị custom field

#### Utility Class:
- **`UserClassificationUtils`**: Các method helper
  - `createDefaultMetadata()`: Tạo metadata mặc định
  - `createDefaultImagesMedia()`: Tạo images media mặc định
  - `createDefaultCustomFields()`: Tạo custom fields mặc định
  - `validatePurchaseQuantity()`: Validate số lượng mua
  - `addCustomFieldToMetadata()`: Thêm custom field
  - `addImageToMedia()`: Thêm hình ảnh

### 3. DTO Updates - Classification DTOs
**File:** `src/modules/business/user/dto/classification.dto.ts`

#### CreateClassificationDto - Thêm fields:
```typescript
@ApiProperty({ description: 'Số lượng tối thiểu mỗi lần mua' })
@IsOptional()
@IsNumber()
@Min(1)
minQuantityPerPurchase?: number;

@ApiProperty({ description: 'Số lượng tối đa mỗi lần mua' })
@IsOptional()
@IsNumber()
@Min(1)
maxQuantityPerPurchase?: number;
```

#### UpdateClassificationDto - Thêm fields tương tự

#### ClassificationResponseDto - Thêm fields:
```typescript
minQuantityPerPurchase?: number | null;
maxQuantityPerPurchase?: number | null;
description?: string | null;
sku?: string | null;
```

### 4. Controller Updates
**File:** `src/modules/business/user/controllers/classification.controller.ts`

#### Cập nhật create method:
```typescript
const createDto: CreateClassificationDto = {
  type: requestBody.type,
  description: requestBody.description,
  price: requestBody.price,
  customFields: requestBody.customFields,
  imagesMediaTypes: requestBody.imagesMediaTypes,
  sku: requestBody.sku,
  availableQuantity: requestBody.availableQuantity,
  minQuantityPerPurchase: requestBody.minQuantityPerPurchase,
  maxQuantityPerPurchase: requestBody.maxQuantityPerPurchase,
};
```

#### Cập nhật update method tương tự

### 5. Service Updates
**File:** `src/modules/business/user/services/classification.service.ts`

#### Cập nhật create method:
```typescript
// Validate số lượng mua nếu có
if (createDto.minQuantityPerPurchase !== undefined || createDto.maxQuantityPerPurchase !== undefined) {
  this.validatePurchaseQuantity(createDto.minQuantityPerPurchase, createDto.maxQuantityPerPurchase);
}

// Tạo phân loại mới
const classification = new UserClassification();
classification.type = createDto.type;
classification.description = createDto.description || null;
classification.sku = createDto.sku || null;
classification.minQuantityPerPurchase = createDto.minQuantityPerPurchase || null;
classification.maxQuantityPerPurchase = createDto.maxQuantityPerPurchase || null;
```

#### Cập nhật update method:
```typescript
if (updateDto.description !== undefined) {
  classification.description = updateDto.description;
}
if (updateDto.sku !== undefined) {
  classification.sku = updateDto.sku;
}
if (updateDto.minQuantityPerPurchase !== undefined) {
  classification.minQuantityPerPurchase = updateDto.minQuantityPerPurchase;
}
if (updateDto.maxQuantityPerPurchase !== undefined) {
  classification.maxQuantityPerPurchase = updateDto.maxQuantityPerPurchase;
}

// Validate số lượng mua nếu có thay đổi
if (updateDto.minQuantityPerPurchase !== undefined || updateDto.maxQuantityPerPurchase !== undefined) {
  const finalMinQuantity = updateDto.minQuantityPerPurchase !== undefined 
    ? updateDto.minQuantityPerPurchase 
    : classification.minQuantityPerPurchase;
  const finalMaxQuantity = updateDto.maxQuantityPerPurchase !== undefined 
    ? updateDto.maxQuantityPerPurchase 
    : classification.maxQuantityPerPurchase;
  this.validatePurchaseQuantity(finalMinQuantity, finalMaxQuantity);
}
```

#### Thêm validation method:
```typescript
private validatePurchaseQuantity(
  minQuantity: number | null | undefined,
  maxQuantity: number | null | undefined
): void {
  if (minQuantity !== null && minQuantity !== undefined && minQuantity < 1) {
    throw new AppException(
      BUSINESS_ERROR_CODES.CLASSIFICATION_VALIDATION_FAILED,
      'Số lượng tối thiểu mỗi lần mua phải lớn hơn 0',
    );
  }

  if (maxQuantity !== null && maxQuantity !== undefined && maxQuantity < 1) {
    throw new AppException(
      BUSINESS_ERROR_CODES.CLASSIFICATION_VALIDATION_FAILED,
      'Số lượng tối đa mỗi lần mua phải lớn hơn 0',
    );
  }

  if (
    minQuantity !== null && minQuantity !== undefined &&
    maxQuantity !== null && maxQuantity !== undefined &&
    minQuantity > maxQuantity
  ) {
    throw new AppException(
      BUSINESS_ERROR_CODES.CLASSIFICATION_VALIDATION_FAILED,
      'Số lượng tối thiểu không được lớn hơn số lượng tối đa',
    );
  }
}
```

#### Cập nhật response methods:
Tất cả các method trả về response đều được cập nhật để bao gồm các trường mới:
- `create()`: Trả về đầy đủ thông tin classification
- `update()`: Trả về đầy đủ thông tin classification đã cập nhật
- `getByProductId()`: Trả về danh sách classification với thông tin đầy đủ
- `getById()`: Trả về chi tiết classification với thông tin đầy đủ

### 6. Type Export
**File:** `src/modules/business/types/index.ts`
```typescript
// Export user classification types
export * from './user-classification.type';
```

## Tính năng mới

### 1. Quản lý số lượng mua
- **minQuantityPerPurchase**: Số lượng tối thiểu mỗi lần mua
- **maxQuantityPerPurchase**: Số lượng tối đa mỗi lần mua
- **Validation**: Kiểm tra min <= max và cả hai > 0

### 2. Thông tin bổ sung
- **description**: Mô tả chi tiết về phân loại
- **sku**: Mã SKU riêng cho từng phân loại

### 3. Custom Fields nâng cao
- **customFields**: Field jsonb riêng biệt cho custom fields
- **Type safety**: Sử dụng `ClassificationCustomFields` interface

### 4. Images Media quản lý
- **imagesMedia**: Field jsonb cho quản lý hình ảnh
- **Type safety**: Sử dụng `ClassificationImagesMedia` interface
- **Metadata**: Thông tin chi tiết về từng hình ảnh

### 5. Metadata nâng cao
- **Type safety**: Sử dụng `UserClassificationMetadata` interface
- **Utility functions**: Helper methods để làm việc với metadata

## Validation Rules

### Số lượng mua:
- `minQuantityPerPurchase` >= 1 (nếu có)
- `maxQuantityPerPurchase` >= 1 (nếu có)
- `minQuantityPerPurchase` <= `maxQuantityPerPurchase` (nếu cả hai có)

### Các trường khác:
- `description`: Optional, có thể null
- `sku`: Optional, có thể null, unique trong phạm vi sản phẩm
- `customFields`: Optional, validate theo schema
- `imagesMedia`: Optional, validate theo format

## Tương thích ngược
- Tất cả các trường mới đều optional
- Không ảnh hưởng đến logic hiện tại
- API responses bao gồm các trường mới với giá trị null nếu chưa set

## Ví dụ sử dụng

### Tạo classification với thông tin đầy đủ:
```json
{
  "type": "Màu đỏ",
  "description": "Phân loại màu đỏ cho sản phẩm áo thun",
  "sku": "SHIRT-RED-001",
  "minQuantityPerPurchase": 1,
  "maxQuantityPerPurchase": 10,
  "price": {
    "listPrice": 300000,
    "salePrice": 250000,
    "currency": "VND"
  }
}
```

### Response:
```json
{
  "id": 123,
  "type": "Màu đỏ",
  "description": "Phân loại màu đỏ cho sản phẩm áo thun",
  "sku": "SHIRT-RED-001",
  "minQuantityPerPurchase": 1,
  "maxQuantityPerPurchase": 10,
  "price": {
    "listPrice": 300000,
    "salePrice": 250000,
    "currency": "VND"
  },
  "customFields": [],
  "imagesMediaTypes": []
}
```
