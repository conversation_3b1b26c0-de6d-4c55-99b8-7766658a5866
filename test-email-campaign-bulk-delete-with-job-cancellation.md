# Test Email Campaign Bulk Delete API với Job Cancellation

## <PERSON><PERSON> tả
Test API xóa nhiều email campaign với tính năng tự động hủy job trong queue cho campaign đang chạy hoặc đã lên lịch.

## API Endpoint
```
DELETE /marketing/email-campaigns
Content-Type: application/json
Authorization: Bearer {JWT_TOKEN}
```

## Request Body
```json
{
  "ids": [1, 2, 3, 4, 5]
}
```

## Test Cases

### 1. Test Bulk Delete - Campaign DRAFT (No Jobs)
```bash
# Tạo campaign DRAFT
POST /marketing/email-campaigns
{
  "title": "Draft Campaign",
  "description": "Campaign ở trạng thái draft",
  "platform": "email",
  "subject": "Draft Subject",
  "content": "<p>Draft content</p>",
  "segmentId": 1,
  "scheduledAt": null
}

# Xóa campaign DRAFT
DELETE /marketing/email-campaigns
{
  "ids": [1]
}

# Expected Response: 200 OK
{
  "success": true,
  "message": "Đã xóa 1 email campaign thành công",
  "data": {
    "deletedCount": 1,
    "failedCount": 0,
    "deletedIds": [1],
    "failedIds": [],
    "message": "Đã xóa 1 email campaign thành công"
  }
}
```

### 2. Test Bulk Delete - Campaign SCHEDULED (Hủy Job)
```bash
# Tạo campaign SCHEDULED với thời gian trong tương lai
POST /marketing/email-campaigns
{
  "title": "Scheduled Campaign",
  "description": "Campaign đã lên lịch",
  "platform": "email", 
  "subject": "Scheduled Subject",
  "content": "<p>Scheduled content</p>",
  "segmentId": 1,
  "scheduledAt": 1735689600  // Thời gian trong tương lai
}

# Xóa campaign SCHEDULED
DELETE /marketing/email-campaigns
{
  "ids": [2]
}

# Expected Response: 200 OK
# Log sẽ hiển thị: "Hủy job cho campaign 2 với status SCHEDULED"
# Log sẽ hiển thị: "✅ Đã hủy job {jobId} cho campaign 2"
{
  "success": true,
  "message": "Đã xóa 1 email campaign thành công",
  "data": {
    "deletedCount": 1,
    "failedCount": 0,
    "deletedIds": [2],
    "failedIds": [],
    "message": "Đã xóa 1 email campaign thành công"
  }
}
```

### 3. Test Bulk Delete - Campaign SENDING (Hủy Job)
```bash
# Tạo campaign và trigger để chuyển sang SENDING
POST /marketing/email-campaigns
{
  "title": "Sending Campaign",
  "description": "Campaign đang gửi",
  "platform": "email",
  "subject": "Sending Subject", 
  "content": "<p>Sending content</p>",
  "segmentId": 1,
  "scheduledAt": null
}

# Giả sử campaign chuyển sang SENDING status

# Xóa campaign SENDING
DELETE /marketing/email-campaigns
{
  "ids": [3]
}

# Expected Response: 200 OK
# Log sẽ hiển thị: "Hủy job cho campaign 3 với status SENDING"
# Nếu job đang active: "⚠️ Job {jobId} đang chạy, không thể hủy"
# Nếu job waiting/delayed: "✅ Đã hủy job {jobId} cho campaign 3"
{
  "success": true,
  "message": "Đã xóa 1 email campaign thành công",
  "data": {
    "deletedCount": 1,
    "failedCount": 0,
    "deletedIds": [3],
    "failedIds": [],
    "message": "Đã xóa 1 email campaign thành công"
  }
}
```

### 4. Test Mixed Status Campaigns
```bash
# Xóa nhiều campaign với status khác nhau
DELETE /marketing/email-campaigns
{
  "ids": [1, 2, 3, 4]  // DRAFT, SCHEDULED, SENDING, SENT
}

# Expected Response: 200 OK
# Log sẽ hiển thị job cancellation cho SCHEDULED và SENDING campaigns
{
  "success": true,
  "message": "Đã xóa 4 email campaign thành công",
  "data": {
    "deletedCount": 4,
    "failedCount": 0,
    "deletedIds": [1, 2, 3, 4],
    "failedIds": [],
    "message": "Đã xóa 4 email campaign thành công"
  }
}
```

### 5. Test Campaign với Job không tồn tại
```bash
# Campaign có jobIds nhưng job đã bị xóa khỏi queue
DELETE /marketing/email-campaigns
{
  "ids": [5]
}

# Expected Response: 200 OK
# Log sẽ hiển thị: "Job {jobId} không tồn tại trong queue"
{
  "success": true,
  "message": "Đã xóa 1 email campaign thành công",
  "data": {
    "deletedCount": 1,
    "failedCount": 0,
    "deletedIds": [5],
    "failedIds": [],
    "message": "Đã xóa 1 email campaign thành công"
  }
}
```

## Job States và Handling

### Job States trong BullMQ:
- **waiting**: Job đang chờ xử lý → **CÓ THỂ HỦY**
- **delayed**: Job bị delay → **CÓ THỂ HỦY**  
- **active**: Job đang chạy → **KHÔNG THỂ HỦY**
- **completed**: Job đã hoàn thành → **KHÔNG CẦN HỦY**
- **failed**: Job thất bại → **KHÔNG CẦN HỦY**

### Logging Messages:
```
✅ Đã hủy job {jobId} cho campaign {campaignId}
⚠️ Job {jobId} đang chạy, không thể hủy
Job {jobId} đã hoàn thành hoặc thất bại, không cần hủy
Job {jobId} không tồn tại trong queue
Campaign {campaignId} không có job nào để hủy
```

## Database Changes

### UserCampaign Entity:
```sql
ALTER TABLE user_campaigns 
ADD COLUMN job_ids JSONB NULL 
COMMENT 'Danh sách ID của job trong queue';
```

### Example jobIds data:
```json
["1234567890", "1234567891"]
```

## Business Logic

1. **Campaign Creation**: Lưu job IDs vào database khi tạo campaign
2. **Job Cancellation**: Hủy job trong queue trước khi xóa campaign
3. **Status Handling**: Chỉ hủy job cho campaign SCHEDULED và SENDING
4. **Error Handling**: Log lỗi nhưng vẫn tiếp tục xóa campaign
5. **Cascade Delete**: Xóa campaign history và campaign

## Notes
- Job cancellation chỉ áp dụng cho campaign có status SCHEDULED hoặc SENDING
- Campaign DRAFT không có job nên không cần hủy
- Campaign SENT/FAILED đã hoàn thành nên không cần hủy job
- Lỗi khi hủy job không làm fail việc xóa campaign
- Job đang active không thể hủy nhưng campaign vẫn được xóa
