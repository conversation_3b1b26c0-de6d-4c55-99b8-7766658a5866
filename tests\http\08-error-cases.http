### Test Error Cases - Invalid Product Type
### Tạo sản phẩm với loại không hợp lệ

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Sản phẩm lỗi",
  "productType": "INVALID_TYPE",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 100000,
    "salePrice": 90000,
    "currency": "VND"
  },
  "description": "Sản phẩm với loại không hợp lệ"
}

### Test Error Cases - Missing Required Fields
### Tạo sản phẩm thiếu trường bắt buộc

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "productType": "PHYSICAL",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 100000,
    "salePrice": 90000,
    "currency": "VND"
  },
  "description": "Sản phẩm thiếu tên"
}

### Test Error Cases - Invalid Price
### Tạo sản phẩm với giá không hợp lệ

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Sản phẩm giá lỗi",
  "productType": "PHYSICAL",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": -100000,
    "salePrice": 90000,
    "currency": "VND"
  },
  "description": "Sản phẩm với giá âm"
}

### Test Error Cases - Physical Product Missing Shipment Config
### Tạo sản phẩm vật lý thiếu thông tin vận chuyển

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Sản phẩm vật lý thiếu shipment",
  "productType": "PHYSICAL",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 100000,
    "salePrice": 90000,
    "currency": "VND"
  },
  "description": "Sản phẩm vật lý thiếu shipmentConfig",
  "imagesMediaTypes": ["image/jpeg"],
  "tags": ["test"]
}

### Test Error Cases - Digital Product Missing Delivery Info
### Tạo sản phẩm số thiếu thông tin giao hàng

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Sản phẩm số thiếu delivery",
  "productType": "DIGITAL",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 100000,
    "salePrice": 90000,
    "currency": "VND"
  },
  "description": "Sản phẩm số thiếu deliveryMethod",
  "imagesMediaTypes": ["image/jpeg"],
  "tags": ["test"],
  "purchaseCount": 0
}

### Test Error Cases - Event Product Missing Event Info
### Tạo sự kiện thiếu thông tin sự kiện

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Sự kiện thiếu thông tin",
  "productType": "EVENT",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 100000,
    "salePrice": 90000,
    "currency": "VND"
  },
  "description": "Sự kiện thiếu eventDate",
  "imagesMediaTypes": ["image/jpeg"],
  "tags": ["test"],
  "purchaseCount": 0
}

### Test Error Cases - Invalid SKU Duplicate
### Tạo sản phẩm với SKU trùng lặp

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Sản phẩm SKU trùng",
  "productType": "PHYSICAL",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 100000,
    "salePrice": 90000,
    "currency": "VND"
  },
  "description": "Sản phẩm với SKU đã tồn tại",
  "imagesMediaTypes": ["image/jpeg"],
  "tags": ["test"],
  "shipmentConfig": {
    "widthCm": 10,
    "heightCm": 10,
    "lengthCm": 10,
    "weightGram": 100
  },
  "inventory": {
    "availableQuantity": 10,
    "sku": "SHIRT-MEN-001"
  }
}

### Test Error Cases - Invalid Warehouse ID
### Tạo sản phẩm với warehouse ID không tồn tại

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Sản phẩm warehouse lỗi",
  "productType": "PHYSICAL",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 100000,
    "salePrice": 90000,
    "currency": "VND"
  },
  "description": "Sản phẩm với warehouse không tồn tại",
  "imagesMediaTypes": ["image/jpeg"],
  "tags": ["test"],
  "shipmentConfig": {
    "widthCm": 10,
    "heightCm": 10,
    "lengthCm": 10,
    "weightGram": 100
  },
  "inventory": {
    "availableQuantity": 10,
    "sku": "TEST-WAREHOUSE-001",
    "warehouseId": 99999
  }
}

### Test Error Cases - Unauthorized Access
### Truy cập không có token

POST {{baseUrl}}/user/products
Content-Type: application/json

{
  "name": "Sản phẩm không có token",
  "productType": "PHYSICAL",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 100000,
    "salePrice": 90000,
    "currency": "VND"
  },
  "description": "Test unauthorized"
}

### Test Error Cases - Invalid Token
### Truy cập với token không hợp lệ

POST {{baseUrl}}/user/products
Authorization: Bearer invalid_token_here
Content-Type: application/json

{
  "name": "Sản phẩm token lỗi",
  "productType": "PHYSICAL",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 100000,
    "salePrice": 90000,
    "currency": "VND"
  },
  "description": "Test invalid token"
}

### Test Error Cases - Get Non-existent Product
### Lấy sản phẩm không tồn tại

GET {{baseUrl}}/user/products/99999
Authorization: Bearer {{token}}

### Test Error Cases - Update Non-existent Product
### Cập nhật sản phẩm không tồn tại

PUT {{baseUrl}}/user/products/99999
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Sản phẩm không tồn tại"
}

### Test Error Cases - Delete Non-existent Product
### Xóa sản phẩm không tồn tại

DELETE {{baseUrl}}/user/products/99999
Authorization: Bearer {{token}}

### Test Error Cases - Invalid Pagination
### Phân trang không hợp lệ

GET {{baseUrl}}/user/products?page=-1&limit=0
Authorization: Bearer {{token}}

### Test Error Cases - Invalid Sort Parameters
### Tham số sắp xếp không hợp lệ

GET {{baseUrl}}/user/products?sortBy=invalid_field&sortDirection=INVALID
Authorization: Bearer {{token}}

### Test Error Cases - Batch Create Empty Products
### Batch create với mảng rỗng

POST {{baseUrl}}/user/products/batch
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "products": []
}

### Test Error Cases - Batch Create Invalid Product in Array
### Batch create với sản phẩm không hợp lệ trong mảng

POST {{baseUrl}}/user/products/batch
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "products": [
    {
      "name": "Sản phẩm hợp lệ",
      "productType": "PHYSICAL",
      "typePrice": "HAS_PRICE",
      "price": {"listPrice": 100000, "salePrice": 90000, "currency": "VND"},
      "description": "OK",
      "imagesMediaTypes": ["image/jpeg"],
      "tags": ["test"],
      "shipmentConfig": {"widthCm": 10, "heightCm": 10, "lengthCm": 10, "weightGram": 100}
    },
    {
      "productType": "PHYSICAL",
      "typePrice": "HAS_PRICE",
      "price": {"listPrice": 100000, "salePrice": 90000, "currency": "VND"},
      "description": "Thiếu tên"
    }
  ]
}

### Test Error Cases - Invalid JSON
### JSON không hợp lệ

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Sản phẩm JSON lỗi",
  "productType": "PHYSICAL",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 100000,
    "salePrice": 90000,
    "currency": "VND"
  },
  "description": "JSON không đóng ngoặc"