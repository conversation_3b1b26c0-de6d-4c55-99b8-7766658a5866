import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, ParseIntPipe, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiExtraModels, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { UserProductService } from '@modules/business/user/services';
import {
  BatchProductResponseDto,
  BulkDeleteProductDto,
  BulkDeleteProductResponseDto,
  BusinessPresignedUrlImageDto,
  BusinessUpdateProductDto,
  BusinessUploadUrlsDto,
  BusinessProductResponseDto as ProductResponseDto,
  QueryProductDto,
  WarehouseListDto,
  PhysicalProductCreateDto,
  DigitalProductCreateDto,
  EventProductCreateDto,
  ServiceProductCreateDto,
  ComboProductCreateDto,
  ProductInventoryDto,
  CreatedProductDto,
  BatchCreateProductsDto
} from '../dto';
import { InventoryResponseDto } from '../dto/inventory';
import { CurrentUser } from '@modules/auth/decorators';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { PRODUCT_CREATE_EXAMPLES, PRODUCT_UPDATE_EXAMPLES } from '../examples/product-examples';


/**
 * Controller xử lý các request liên quan đến sản phẩm của người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_BUSINESS_PRODUCT)
@Controller('user/products')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(ApiResponseDto, PaginatedResult, ProductResponseDto, BusinessPresignedUrlImageDto, BusinessUploadUrlsDto, PhysicalProductCreateDto, DigitalProductCreateDto, EventProductCreateDto, ServiceProductCreateDto, ComboProductCreateDto, BusinessUpdateProductDto, BulkDeleteProductDto, BulkDeleteProductResponseDto, ProductInventoryDto, InventoryResponseDto, WarehouseListDto, BatchCreateProductsDto)
export class UserProductController {
  constructor(private readonly userProductService: UserProductService) {}

  /**
   * Tạo sản phẩm mới
   * @param createProductDto DTO chứa thông tin sản phẩm mới (có thể bao gồm thông tin tồn kho)
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm đã tạo (có thể bao gồm thông tin tồn kho nếu được tạo)
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Tạo sản phẩm mới',
    description: 'Tạo sản phẩm mới với khả năng tạo thông tin tồn kho cùng lúc. Chỉ sản phẩm PHYSICAL cần thông tin tồn kho. Sản phẩm DIGITAL, SERVICE, EVENT và COMBO không cần inventory và shipmentConfig. Có thể sử dụng inventory có sẵn bằng inventoryId hoặc tạo inventory mới.'
  })
  @ApiBody({
    description: 'Product creation data - supports multiple product types',
    examples: PRODUCT_CREATE_EXAMPLES,
    schema: {
      oneOf: [
        { $ref: '#/components/schemas/PhysicalProductCreateDto' },
        { $ref: '#/components/schemas/DigitalProductCreateDto' },
        { $ref: '#/components/schemas/EventProductCreateDto' },
        { $ref: '#/components/schemas/ServiceProductCreateDto' },
        { $ref: '#/components/schemas/ComboProductCreateDto' }
      ]
    }
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Sản phẩm đã được tạo thành công (có thể bao gồm thông tin tồn kho)',
    type: () => ApiResponseDto.getSchema(ProductResponseDto),
  })
  async createProduct(
    @Body() createProductDto: CreatedProductDto,
    @CurrentUser('id') userId: number,
  ) {
    const product = await this.userProductService.createProduct(createProductDto, userId);
    return ApiResponseDto.created<ProductResponseDto>(product, 'Tạo sản phẩm thành công');
  }

  /**
   * Tạo nhiều sản phẩm cùng lúc
   * @param batchCreateDto DTO chứa danh sách sản phẩm cần tạo
   * @param userId ID của người dùng hiện tại
   * @returns Kết quả tạo batch với thông tin thành công và thất bại
   */
  @Post('batch')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Tạo nhiều sản phẩm cùng lúc',
    description: 'Tạo nhiều sản phẩm cùng lúc. API sẽ xử lý từng sản phẩm một cách tuần tự và trả về kết quả chi tiết cho từng sản phẩm. Hỗ trợ tối đa 50 sản phẩm mỗi lần.'
  })
  @ApiBody({
    type: BatchCreateProductsDto,
    description: 'Danh sách sản phẩm cần tạo - hỗ trợ tất cả loại sản phẩm (Physical, Digital, Event, Service, Combo)'
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Kết quả tạo batch sản phẩm',
    type: () => ApiResponseDto.getSchema(BatchProductResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ hoặc vượt quá giới hạn 50 sản phẩm',
  })
  async batchCreateProducts(
    @Body() batchCreateDto: BatchCreateProductsDto,
    @CurrentUser('id') userId: number,
  ) {
    const result = await this.userProductService.batchCreateProducts(batchCreateDto, userId);

    // Nếu tất cả sản phẩm đều tạo thành công
    if (result.failedCount === 0) {
      return ApiResponseDto.created<BatchProductResponseDto>(result, 'Tạo tất cả sản phẩm thành công');
    }

    // Nếu có một số sản phẩm thất bại
    if (result.successCount > 0) {
      return ApiResponseDto.success<BatchProductResponseDto>(result, `Tạo thành công ${result.successCount}/${result.totalProducts} sản phẩm`);
    }

    // Nếu tất cả sản phẩm đều thất bại
    return ApiResponseDto.success<BatchProductResponseDto>(result, 'Không có sản phẩm nào được tạo thành công');
  }

  /**
   * Lấy danh sách sản phẩm
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách sản phẩm với phân trang
   */
  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Lấy danh sách sản phẩm' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách sản phẩm',
    type: () => ApiResponseDto.getPaginatedSchema(ProductResponseDto),
  })
  async getProducts(@Query() queryDto: QueryProductDto) {
    const products = await this.userProductService.getProducts(queryDto);
    return ApiResponseDto.success<PaginatedResult<ProductResponseDto>>(products, 'Lấy danh sách sản phẩm thành công');
  }

  /**
   * Lấy danh sách kho để chọn
   * @returns Danh sách kho
   */
  @Get('warehouses')
  @ApiOperation({
    summary: 'Lấy danh sách kho',
    description: 'Lấy danh sách tất cả các kho có thể sử dụng để quản lý tồn kho sản phẩm.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách kho thành công',
    type: [WarehouseListDto],
  })
  async getWarehouseList() {
    const warehouses = await this.userProductService.getWarehouseList();
    return ApiResponseDto.success(warehouses, 'Lấy danh sách kho thành công');
  }

  /**
   * Lấy chi tiết sản phẩm theo ID
   * @param id ID của sản phẩm
   * @returns Chi tiết sản phẩm
   */
  @Get(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Lấy chi tiết sản phẩm' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Chi tiết sản phẩm',
    type: () => ApiResponseDto.getSchema(ProductResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy sản phẩm',
  })
  async getProductDetail(@Param('id', ParseIntPipe) id: number) {
    const product = await this.userProductService.getProductDetail(id);
    return ApiResponseDto.success<ProductResponseDto>(product, 'Lấy chi tiết sản phẩm thành công');
  }

  /**
   * Cập nhật sản phẩm
   * @param id ID của sản phẩm cần cập nhật
   * @param updateProductDto DTO chứa thông tin cập nhật
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm đã cập nhật
   */
  @Put(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Cập nhật sản phẩm',
    description: 'Cập nhật thông tin sản phẩm bao gồm tên, giá, mô tả, hình ảnh, tags, custom fields, phân loại (thêm/sửa/xóa với ảnh riêng), tồn kho và thông tin nâng cao. Tất cả các trường đều optional. EVENT products không cần inventory và shipmentConfig. Có thể xóa phân loại bằng classificationsToDelete.'
  })
  @ApiBody({
    type: BusinessUpdateProductDto,
    examples: PRODUCT_UPDATE_EXAMPLES
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Sản phẩm đã được cập nhật thành công. Response bao gồm: thông tin sản phẩm đầy đủ, inventory (cho sản phẩm PHYSICAL), uploadUrls (nếu có ảnh mới), classifications (nếu có), status và detail_id.',
    type: () => ApiResponseDto.getSchema(ProductResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy sản phẩm',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu không hợp lệ, SKU đã tồn tại, inventory không hợp lệ, hoặc thiếu inventory cho sản phẩm PHYSICAL',
  })
  async updateProduct(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateProductDto: BusinessUpdateProductDto,
    @CurrentUser('id') userId: number,
  ) {
    const product = await this.userProductService.updateProduct(id, updateProductDto, userId);
    return ApiResponseDto.success<ProductResponseDto>(product, 'Cập nhật sản phẩm thành công');
  }

  /**
   * Xóa nhiều sản phẩm (soft delete)
   * @param bulkDeleteDto DTO chứa danh sách ID sản phẩm cần xóa
   * @param userId ID của người dùng hiện tại
   * @returns Kết quả xóa nhiều sản phẩm
   */
  @Delete('bulk')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Xóa nhiều sản phẩm' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa nhiều sản phẩm thành công',
    type: () => ApiResponseDto.getSchema(BulkDeleteProductResponseDto),
  })
  @ApiResponse({
    status: 207,
    description: 'Một số sản phẩm không thể xóa',
    type: () => ApiResponseDto.getSchema(BulkDeleteProductResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  async bulkDeleteProducts(
    @Body() bulkDeleteDto: BulkDeleteProductDto,
    @CurrentUser('id') userId: number,
  ) {
    const result = await this.userProductService.bulkDeleteProducts(bulkDeleteDto, userId);

    return ApiResponseDto.success(result, result.message);
  }

  /**
   * Xóa sản phẩm (soft delete)
   * @param id ID của sản phẩm cần xóa
   * @param userId ID của người dùng hiện tại
   * @returns Thông báo xóa thành công
   */
  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Xóa sản phẩm' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Sản phẩm đã được xóa thành công',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy sản phẩm',
  })
  async deleteProduct(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ) {
    await this.userProductService.deleteProduct(id, userId);
    return ApiResponseDto.success(null, 'Sản phẩm đã được xóa thành công');
  }

  // ==================== INVENTORY MANAGEMENT ENDPOINTS ====================

  /**
   * Lấy thông tin tồn kho của sản phẩm
   * @param id ID sản phẩm
   * @param warehouseId ID kho (optional)
   * @param userId ID người dùng hiện tại
   * @returns Danh sách thông tin tồn kho của sản phẩm
   */
  @Get(':id/inventory')
  @ApiOperation({
    summary: 'Lấy thông tin tồn kho của sản phẩm',
    description: 'Lấy thông tin tồn kho của sản phẩm theo kho. Nếu không chỉ định kho, sẽ trả về tồn kho của tất cả các kho.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy thông tin tồn kho thành công',
    type: [InventoryResponseDto],
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy sản phẩm',
  })
  async getProductInventory(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
    @Query('warehouseId') warehouseId?: string,
  ) {
    const inventory = await this.userProductService.getProductInventory(
      id,
      warehouseId ? parseInt(warehouseId) : null,
      userId,
    );
    return ApiResponseDto.success(inventory, 'Lấy thông tin tồn kho thành công');
  }

  /**
   * Tạo hoặc cập nhật tồn kho cho sản phẩm
   * @param id ID sản phẩm
   * @param inventoryData Dữ liệu tồn kho
   * @param userId ID người dùng hiện tại
   * @returns Thông tin tồn kho đã tạo/cập nhật
   */
  @Post(':id/inventory')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Tạo hoặc cập nhật tồn kho cho sản phẩm',
    description: 'Tạo mới hoặc cập nhật thông tin tồn kho cho sản phẩm trong kho cụ thể. Nếu đã có tồn kho cho sản phẩm và kho này, sẽ cập nhật. Nếu chưa có, sẽ tạo mới.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo/cập nhật tồn kho thành công',
    type: InventoryResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy sản phẩm hoặc kho',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu không hợp lệ',
  })
  async createOrUpdateProductInventory(
    @Param('id', ParseIntPipe) id: number,
    @Body() inventoryData: ProductInventoryDto,
    @CurrentUser('id') userId: number,
  ) {
    const inventory = await this.userProductService.createOrUpdateProductInventory(
      id,
      inventoryData,
      userId,
    );
    return ApiResponseDto.created(inventory, 'Tạo/cập nhật tồn kho thành công');
  }

}
