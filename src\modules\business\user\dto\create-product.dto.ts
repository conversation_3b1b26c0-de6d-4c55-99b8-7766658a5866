import { ApiProperty, ApiExtraModels } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  MaxLength,
  IsEnum,
  IsOptional,
  IsArray,
  IsObject,
  ValidateNested,
  IsNumber,
  Min,
  Max,
  IsBoolean,
} from 'class-validator';
import { PriceTypeEnum, ProductTypeEnum } from '@modules/business/enums';

// Re-export các DTO cần thiết từ các file khác
export { ClassificationPriceDto, ClassificationStringPriceDto } from './classification.dto';
export { HasPriceDto, StringPriceDto } from './price.dto';
export { CustomFieldInputDto } from './custom-field-metadata.dto';
export { CreateClassificationDto } from './classification.dto';
export {
  DigitalAdvancedInfoDto,
  EventAdvancedInfoDto,
  ServiceAdvancedInfoDto,
  ComboAdvancedInfoDto
} from './advanced-info';

// Import các DTO để sử dụng trong BusinessCreateProductDto
import { HasPriceDto, StringPriceDto } from './price.dto';
import { ClassificationPriceDto, ClassificationStringPriceDto } from './classification.dto';
import { CustomFieldInputDto } from './custom-field-metadata.dto';
import { CreateClassificationDto } from './classification.dto';
import {
  DigitalAdvancedInfoDto,
  EventAdvancedInfoDto,
  ServiceAdvancedInfoDto,
  ComboAdvancedInfoDto
} from './advanced-info';

/**
 * DTO cho cấu hình vận chuyển sản phẩm
 */
export class BusinessShipmentConfigDto {
  @ApiProperty({
    description: 'Chiều rộng (cm)',
    example: 25,
    minimum: 0,
    maximum: 1000,
  })
  @IsNumber()
  @Min(0)
  @Max(1000)
  widthCm: number;

  @ApiProperty({
    description: 'Chiều cao (cm)',
    example: 5,
    minimum: 0,
    maximum: 1000,
  })
  @IsNumber()
  @Min(0)
  @Max(1000)
  heightCm: number;

  @ApiProperty({
    description: 'Chiều dài (cm)',
    example: 30,
    minimum: 0,
    maximum: 1000,
  })
  @IsNumber()
  @Min(0)
  @Max(1000)
  lengthCm: number;

  @ApiProperty({
    description: 'Trọng lượng (gram)',
    example: 200,
    minimum: 0,
    maximum: 100000,
  })
  @IsNumber()
  @Min(0)
  @Max(100000)
  weightGram: number;
}

/**
 * DTO cho thông tin inventory sản phẩm
 */
export class ProductInventoryDto {
  @ApiProperty({
    description: 'ID warehouse (nếu tạo inventory mới)',
    example: 1,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  warehouseId?: number;

  @ApiProperty({
    description: 'ID inventory hiện có (nếu sử dụng inventory có sẵn)',
    example: 123,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  inventoryId?: number;

  @ApiProperty({
    description: 'Số lượng có sẵn (chỉ dùng khi tạo inventory mới)',
    example: 100,
    minimum: 0,
    required: false,
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  availableQuantity?: number;

  @ApiProperty({
    description: 'SKU sản phẩm (chỉ dùng khi tạo inventory mới)',
    example: 'SHIRT-001',
    maxLength: 100,
    required: false,
  })
  @IsString()
  @MaxLength(100)
  @IsOptional()
  sku?: string;

  @ApiProperty({
    description: 'Barcode sản phẩm (chỉ dùng khi tạo inventory mới)',
    example: '1234567890123',
    maxLength: 50,
    required: false,
  })
  @IsString()
  @MaxLength(50)
  @IsOptional()
  barcode?: string;
}

// Union type cho price
export type ProductPriceDto = HasPriceDto | StringPriceDto | ClassificationPriceDto | ClassificationStringPriceDto;

/**
 * DTO cho việc tạo sản phẩm mới trong module business
 * ⚠️ DEPRECATED: Sử dụng CreatedProductDto từ @/src/modules/business/user/dto/base/created-product.dto.ts thay thế
 */
@ApiExtraModels(DigitalAdvancedInfoDto, EventAdvancedInfoDto, ServiceAdvancedInfoDto, ComboAdvancedInfoDto, HasPriceDto, StringPriceDto, ClassificationPriceDto, ClassificationStringPriceDto)
export class BusinessCreateProductDto {
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Áo thun nam',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: 'Loại sản phẩm',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.PHYSICAL,
  })
  @IsEnum(ProductTypeEnum)
  @IsNotEmpty()
  productType: ProductTypeEnum;

  @ApiProperty({
    description: 'Loại giá sản phẩm',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
  })
  @IsEnum(PriceTypeEnum)
  @IsNotEmpty()
  typePrice: PriceTypeEnum;

  @ApiProperty({
    description: 'Thông tin giá sản phẩm (tùy thuộc vào typePrice)',
    oneOf: [
      { $ref: '#/components/schemas/HasPriceDto' },
      { $ref: '#/components/schemas/StringPriceDto' },
      { $ref: '#/components/schemas/ClassificationPriceDto' },
      { $ref: '#/components/schemas/ClassificationStringPriceDto' }
    ],
    required: false,
  })
  @IsOptional()
  @IsObject()
  price?: ProductPriceDto;

  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'Áo thun nam chất liệu cotton cao cấp, form regular fit',
    required: false,
    maxLength: 2000,
  })
  @IsOptional()
  @IsString()
  @MaxLength(2000)
  description?: string;

  @ApiProperty({
    description: 'Danh sách loại media của hình ảnh sản phẩm',
    type: [String],
    example: ['image/jpeg', 'image/png'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  imagesMediaTypes?: string[];

  @ApiProperty({
    description: 'Tags của sản phẩm',
    type: [String],
    example: ['áo thun', 'nam', 'cotton', 'basic'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({
    description: 'Danh sách custom fields',
    type: [CustomFieldInputDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomFieldInputDto)
  customFields?: CustomFieldInputDto[];

  @ApiProperty({
    description: 'Cấu hình vận chuyển sản phẩm',
    type: () => BusinessShipmentConfigDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => BusinessShipmentConfigDto)
  shipmentConfig?: BusinessShipmentConfigDto;

  @ApiProperty({
    description: 'Thông tin tồn kho (chỉ áp dụng cho sản phẩm PHYSICAL)',
    type: ProductInventoryDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductInventoryDto)
  inventory?: ProductInventoryDto;

  @ApiProperty({
    description: 'Thông tin nâng cao của sản phẩm (tùy thuộc vào loại sản phẩm)',
    oneOf: [
      { $ref: '#/components/schemas/DigitalAdvancedInfoDto' },
      { $ref: '#/components/schemas/EventAdvancedInfoDto' },
      { $ref: '#/components/schemas/ServiceAdvancedInfoDto' },
      { $ref: '#/components/schemas/ComboAdvancedInfoDto' }
    ],
    required: false,
  })
  @IsOptional()
  @IsObject()
  advancedInfo?: DigitalAdvancedInfoDto | EventAdvancedInfoDto | ServiceAdvancedInfoDto | ComboAdvancedInfoDto;

  @ApiProperty({
    description: 'Danh sách phân loại sản phẩm',
    type: [CreateClassificationDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateClassificationDto)
  classifications?: CreateClassificationDto[];
}

/**
 * DTO cho việc tạo nhiều sản phẩm cùng lúc
 * ⚠️ DEPRECATED: Sử dụng CreatedProductDto từ @/src/modules/business/user/dto/base/created-product.dto.ts thay thế
 */
export class BusinessBatchCreateProductDto {
  @ApiProperty({
    description: 'Danh sách sản phẩm cần tạo',
    type: [BusinessCreateProductDto],
  })
  @IsArray()
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => BusinessCreateProductDto)
  products: BusinessCreateProductDto[];
}
