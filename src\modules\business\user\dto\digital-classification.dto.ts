import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsArray,
  IsObject,
  ValidateNested,
  Min,
} from 'class-validator';
import { HasPriceDto } from './price.dto';
import { CustomFieldInputDto } from './custom-field-metadata.dto';

/**
 * DTO cho Digital Classification (chuyển đổi từ VariantDto)
 * Đại diện cho các phân loại/biến thể của sản phẩm số
 */
export class DigitalClassificationDto {
  @ApiProperty({
    description: 'Tên phân loại',
    example: 'Basic',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Mã SKU của phân loại',
    example: 'BASIC-001',
  })
  @IsString()
  @IsNotEmpty()
  sku: string;

  @ApiProperty({
    description: '<PERSON><PERSON> lượng có sẵn',
    example: 1,
  })
  @IsNumber()
  @Min(0)
  availableQuantity: number;

  @ApiProperty({
    description: 'Số lượng tối thiểu mỗi lần mua',
    example: 1,
  })
  @IsNumber()
  @Min(1)
  minQuantityPerPurchase: number;

  @ApiProperty({
    description: 'Số lượng tối đa mỗi lần mua',
    example: 1,
  })
  @IsNumber()
  @Min(1)
  maxQuantityPerPurchase: number;

  @ApiProperty({
    description: 'Giá của phân loại',
    type: HasPriceDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => HasPriceDto)
  price: HasPriceDto;

  @ApiProperty({
    description: 'Danh sách custom fields',
    type: [CustomFieldInputDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomFieldInputDto)
  customFields?: CustomFieldInputDto[];

  @ApiProperty({
    description: 'Danh sách loại hình ảnh',
    type: [String],
    example: ['image/jpeg'],
  })
  @IsArray()
  @IsString({ each: true })
  imagesMediaTypes: string[];

  @ApiProperty({
    description: 'Mô tả phân loại',
    example: 'Phiên bản cơ bản - Học React cơ bản',
  })
  @IsString()
  @IsNotEmpty()
  description: string;
}

/**
 * DTO cho Digital Classification Response
 */
export class DigitalClassificationResponseDto {
  @ApiProperty({
    description: 'ID của phân loại',
    example: 123,
  })
  id: number;

  @ApiProperty({
    description: 'Tên phân loại',
    example: 'Basic',
  })
  name: string;

  @ApiProperty({
    description: 'Mã SKU của phân loại',
    example: 'BASIC-001',
  })
  sku: string;

  @ApiProperty({
    description: 'Số lượng có sẵn',
    example: 1,
  })
  availableQuantity: number;

  @ApiProperty({
    description: 'Số lượng tối thiểu mỗi lần mua',
    example: 1,
  })
  minQuantityPerPurchase: number;

  @ApiProperty({
    description: 'Số lượng tối đa mỗi lần mua',
    example: 1,
  })
  maxQuantityPerPurchase: number;

  @ApiProperty({
    description: 'Giá của phân loại',
    type: HasPriceDto,
  })
  price: HasPriceDto;

  @ApiProperty({
    description: 'Danh sách custom fields',
    type: [CustomFieldInputDto],
  })
  customFields: CustomFieldInputDto[];

  @ApiProperty({
    description: 'Danh sách hình ảnh với URL CDN',
    type: [Object],
    example: [
      {
        key: 'business/IMAGE/2024/01/classification-123-image-0-1704067200000',
        position: 0,
        url: 'https://cdn.example.com/business/IMAGE/2024/01/classification-123-image-0-1704067200000'
      }
    ],
  })
  imagesMediaTypes: Array<{
    key: string;
    position: number;
    url: string;
  }>;

  @ApiProperty({
    description: 'Mô tả phân loại',
    example: 'Phiên bản cơ bản - Học React cơ bản',
  })
  description: string;

  @ApiProperty({
    description: 'Upload URLs cho hình ảnh (nếu có)',
    required: false,
  })
  uploadUrls?: {
    classificationId: number;
    imagesUploadUrls: Array<{
      url: string;
      key: string;
      index: number;
    }>;
  };
}

/**
 * DTO cho Update Digital Classification
 */
export class UpdateDigitalClassificationDto {
  @ApiProperty({
    description: 'ID của phân loại',
    example: 123,
  })
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'Tên phân loại',
    example: 'Basic',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  name?: string;

  @ApiProperty({
    description: 'Mã SKU của phân loại',
    example: 'BASIC-001',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  sku?: string;

  @ApiProperty({
    description: 'Số lượng có sẵn',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  availableQuantity?: number;

  @ApiProperty({
    description: 'Số lượng tối thiểu mỗi lần mua',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  minQuantityPerPurchase?: number;

  @ApiProperty({
    description: 'Số lượng tối đa mỗi lần mua',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  maxQuantityPerPurchase?: number;

  @ApiProperty({
    description: 'Giá của phân loại',
    type: HasPriceDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => HasPriceDto)
  price?: HasPriceDto;

  @ApiProperty({
    description: 'Danh sách custom fields',
    type: [CustomFieldInputDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomFieldInputDto)
  customFields?: CustomFieldInputDto[];

  @ApiProperty({
    description: 'Danh sách operations cho hình ảnh',
    type: [Object],
    required: false,
    example: [
      {
        operation: 'ADD',
        mimeType: 'image/jpeg'
      },
      {
        operation: 'DELETE',
        key: 'business/IMAGE/2024/01/classification-123-image-0-1704067200000'
      }
    ],
  })
  @IsOptional()
  @IsArray()
  imageOperations?: Array<{
    operation: 'ADD' | 'DELETE';
    position?: number;
    key?: string;
    mimeType?: string;
  }>;

  @ApiProperty({
    description: 'Mô tả phân loại',
    example: 'Phiên bản cơ bản - Học React cơ bản',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  description?: string;
}
