### Test Get Products List
### L<PERSON>y danh sách sản phẩm với phân trang

GET {{baseUrl}}/user/products?page=1&limit=10
Authorization: Bearer {{token}}

### Test Get Products List with Filters
### L<PERSON>y danh sách sản phẩm với bộ lọc

GET {{baseUrl}}/user/products?page=1&limit=10&productType=PHYSICAL&status=ACTIVE&search=áo
Authorization: Bearer {{token}}

### Test Get Products List with Sorting
### Lấy danh sách sản phẩm với sắp xếp

GET {{baseUrl}}/user/products?page=1&limit=10&sortBy=createdAt&sortDirection=DESC
Authorization: Bearer {{token}}

### Test Get Product Detail
### Lấy chi tiết sản phẩm

GET {{baseUrl}}/user/products/1
Authorization: Bearer {{token}}

### Test Update Physical Product
### Cập nhật sản phẩm vật lý

PUT {{baseUrl}}/user/products/1
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Áo thun nam cao cấp - Updated",
  "price": {
    "listPrice": 350000,
    "salePrice": 300000,
    "currency": "VND"
  },
  "description": "Áo thun nam chất liệu cotton 100% cao cấp - Phiên bản cập nhật",
  "tags": ["thời trang", "nam", "áo thun", "cotton", "updated"],
  "shipmentConfig": {
    "widthCm": 26,
    "heightCm": 6,
    "lengthCm": 31,
    "weightGram": 220
  },
  "inventory": {
    "availableQuantity": 120,
    "sku": "SHIRT-MEN-001-V2"
  }
}

### Test Update Product with New Images
### Cập nhật sản phẩm với hình ảnh mới

PUT {{baseUrl}}/user/products/1
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Áo thun nam với ảnh mới",
  "imagesMediaTypes": ["image/jpeg", "image/png", "image/webp"],
  "imageOperations": [
    {
      "operation": "ADD",
      "mediaType": "image/jpeg"
    },
    {
      "operation": "DELETE",
      "position": 0
    }
  ]
}

### Test Update Product Classifications
### Cập nhật phân loại sản phẩm

PUT {{baseUrl}}/user/products/1
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "classifications": [
    {
      "name": "Size S - Updated",
      "price": {
        "listPrice": 290000,
        "salePrice": 240000,
        "currency": "VND"
      },
      "description": "Size S cho người từ 50-55kg - Cập nhật",
      "metadata": {
        "size": "S",
        "color": "Trắng",
        "material": "Cotton 100%"
      }
    },
    {
      "name": "Size XL",
      "price": {
        "listPrice": 320000,
        "salePrice": 270000,
        "currency": "VND"
      },
      "description": "Size XL cho người từ 70-80kg",
      "metadata": {
        "size": "XL",
        "color": "Đen",
        "material": "Cotton 100%"
      }
    }
  ],
  "classificationsToDelete": [2]
}

### Test Delete Single Product
### Xóa một sản phẩm

DELETE {{baseUrl}}/user/products/1
Authorization: Bearer {{token}}

### Test Bulk Delete Products
### Xóa nhiều sản phẩm cùng lúc

DELETE {{baseUrl}}/user/products/bulk
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "productIds": [1, 2, 3, 4, 5]
}

### Test Get Product Inventory
### Lấy thông tin tồn kho sản phẩm

GET {{baseUrl}}/user/products/1/inventory
Authorization: Bearer {{token}}

### Test Get Product Inventory by Warehouse
### Lấy thông tin tồn kho theo kho

GET {{baseUrl}}/user/products/1/inventory?warehouseId=1
Authorization: Bearer {{token}}

### Test Create/Update Product Inventory
### Tạo hoặc cập nhật tồn kho sản phẩm

POST {{baseUrl}}/user/products/1/inventory
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "availableQuantity": 200,
  "sku": "SHIRT-MEN-001-NEW",
  "barcode": "2222222222222",
  "warehouseId": 1
}

### Test Get Warehouse List
### Lấy danh sách kho

GET {{baseUrl}}/user/products/warehouses
Authorization: Bearer {{token}}

### Test Search Products
### Tìm kiếm sản phẩm

GET {{baseUrl}}/user/products?search=áo thun&productType=PHYSICAL
Authorization: Bearer {{token}}

### Test Filter Products by Price Range
### Lọc sản phẩm theo khoảng giá

GET {{baseUrl}}/user/products?minPrice=100000&maxPrice=500000
Authorization: Bearer {{token}}

### Test Filter Products by Tags
### Lọc sản phẩm theo tags

GET {{baseUrl}}/user/products?tags=thời trang,nam
Authorization: Bearer {{token}}

### Test Get Products with Pagination
### Lấy sản phẩm với phân trang lớn

GET {{baseUrl}}/user/products?page=1&limit=50
Authorization: Bearer {{token}}

### Test Get Products by Status
### Lấy sản phẩm theo trạng thái

GET {{baseUrl}}/user/products?status=PENDING
Authorization: Bearer {{token}}

### Test Get Products by Date Range
### Lấy sản phẩm theo khoảng thời gian

GET {{baseUrl}}/user/products?fromDate=2024-01-01&toDate=2024-12-31
Authorization: Bearer {{token}}

### Test Update Product Status
### Cập nhật trạng thái sản phẩm

PUT {{baseUrl}}/user/products/1
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "status": "ACTIVE"
}

### Test Update Product with Custom Fields
### Cập nhật sản phẩm với custom fields

PUT {{baseUrl}}/user/products/1
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "customFields": [
    {
      "fieldId": 1,
      "fieldValue": "Giá trị custom field 1"
    },
    {
      "fieldId": 2,
      "fieldValue": "Giá trị custom field 2"
    }
  ]
}

### Test Complex Product Update
### Cập nhật sản phẩm phức tạp

PUT {{baseUrl}}/user/products/1
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Áo thun nam premium - Final Version",
  "price": {
    "listPrice": 400000,
    "salePrice": 350000,
    "currency": "VND"
  },
  "description": "Áo thun nam chất liệu premium, thiết kế hiện đại",
  "tags": ["premium", "thời trang", "nam", "cotton"],
  "imagesMediaTypes": ["image/jpeg", "image/png"],
  "shipmentConfig": {
    "widthCm": 30,
    "heightCm": 8,
    "lengthCm": 35,
    "weightGram": 300
  },
  "inventory": {
    "availableQuantity": 150,
    "sku": "SHIRT-PREMIUM-001",
    "barcode": "3333333333333"
  },
  "classifications": [
    {
      "name": "Size M Premium",
      "price": {
        "listPrice": 400000,
        "salePrice": 350000,
        "currency": "VND"
      },
      "description": "Size M chất liệu premium",
      "metadata": {
        "size": "M",
        "material": "Cotton Premium",
        "origin": "Vietnam"
      }
    }
  ],
  "customFields": [
    {
      "fieldId": 1,
      "fieldValue": "Premium Collection"
    }
  ]
}
