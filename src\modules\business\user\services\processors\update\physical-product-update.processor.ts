import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import {
  UserProductRepository,
  InventoryRepository,
  PhysicalWarehouseRepository,
} from '@modules/business/repositories';
import { BusinessUpdateProductDto, ProductInventoryDto } from '../../../dto';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { UserProduct, Inventory } from '@modules/business/entities';
import { ValidationHelper } from '../../../helpers/validation.helper';
import { InventoryResponseDto } from '../../../dto/inventory';
import { UpdateProductResult } from './update-product-orchestrator';

/**
 * Processor chuyên xử lý cập nhật sản phẩm vật lý (PHYSICAL)
 * Xử lý inventory, shipment config, warehouse management
 */
@Injectable()
export class PhysicalProductUpdateProcessor {
  private readonly logger = new Logger(PhysicalProductUpdateProcessor.name);

  constructor(
    private readonly userProductRepository: UserProductRepository,
    private readonly inventoryRepository: InventoryRepository,
    private readonly physicalWarehouseRepository: PhysicalWarehouseRepository,
    private readonly validationHelper: ValidationHelper,
  ) {}

  /**
   * Cập nhật sản phẩm vật lý hoàn chỉnh
   */
  @Transactional()
  async updatePhysicalProduct(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<UpdateProductResult> {
    this.logger.log(
      `Updating PHYSICAL product: ${product.name} (ID: ${product.id})`,
    );

    // BƯỚC 1: Validate dữ liệu đầu vào cho sản phẩm vật lý
    await this.validatePhysicalProductData(updateDto);

    // BƯỚC 2: Cập nhật shipment config
    this.updateShipmentConfig(product, updateDto);

    // BƯỚC 3: Xử lý inventory update
    const inventory = await this.processInventoryUpdate(
      product,
      updateDto,
      userId,
    );

    // BƯỚC 4: Lưu sản phẩm đã cập nhật
    const updatedProduct = await this.userProductRepository.save(product);

    return {
      product: updatedProduct,
      imagesUploadUrls: [],
      advancedImagesUploadUrls: [],
      classificationUploadUrls: [],
      classifications: [],
      inventory,
    };
  }

  /**
   * Validate dữ liệu đầu vào cho sản phẩm vật lý
   */
  private async validatePhysicalProductData(
    updateDto: BusinessUpdateProductDto,
  ): Promise<void> {
    // Validate shipment config nếu có
    if (updateDto.shipmentConfig) {
      this.validateShipmentConfig(updateDto.shipmentConfig);
    }

    // Validate inventory data nếu có
    if (updateDto.inventory) {
      await this.validateInventoryData(updateDto.inventory);
    }
  }

  /**
   * Validate shipment config
   */
  private validateShipmentConfig(shipmentConfig: unknown): void {
    const config = shipmentConfig as Record<string, unknown>;

    if (config.widthCm !== undefined && (config.widthCm as number) < 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Chiều rộng không thể âm',
      );
    }

    if (config.heightCm !== undefined && (config.heightCm as number) < 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Chiều cao không thể âm',
      );
    }

    if (config.lengthCm !== undefined && (config.lengthCm as number) < 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Chiều dài không thể âm',
      );
    }

    if (config.weightGram !== undefined && (config.weightGram as number) < 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Trọng lượng không thể âm',
      );
    }
  }

  /**
   * Validate inventory data
   */
  private async validateInventoryData(inventoryData: any): Promise<void> {
    if (Array.isArray(inventoryData)) {
      for (const item of inventoryData) {
        if (
          item.availableQuantity !== undefined &&
          item.availableQuantity < 0
        ) {
          throw new AppException(
            BUSINESS_ERROR_CODES.INVALID_INPUT,
            'Số lượng tồn kho không thể âm',
          );
        }

        // Validate warehouse nếu có
        if (item.warehouseId) {
          await this.validateWarehouse(item.warehouseId);
        }

        // Validate SKU unique nếu có
        if (item.sku) {
          await this.validateSkuUnique(item.sku, item.productId);
        }
      }
    } else if (
      inventoryData.availableQuantity !== undefined &&
      inventoryData.availableQuantity < 0
    ) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Số lượng tồn kho không thể âm',
      );
    }
  }

  /**
   * Validate warehouse tồn tại
   */
  private async validateWarehouse(warehouseId: number): Promise<void> {
    const warehouse =
      await this.physicalWarehouseRepository.findByWarehouseId_user(
        warehouseId,
      );
    if (!warehouse) {
      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND,
        `Kho với ID ${warehouseId} không tồn tại`,
      );
    }
  }

  /**
   * Validate SKU unique
   */
  private async validateSkuUnique(
    sku: string,
    productId: number,
  ): Promise<void> {
    const existingInventory = await this.inventoryRepository.findBySkuAndUserId(
      sku,
      productId,
    );
    if (existingInventory && existingInventory.productId !== productId) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVENTORY_CREATION_FAILED,
        `Mã SKU "${sku}" đã tồn tại trong sản phẩm khác`,
      );
    }
  }

  /**
   * Cập nhật shipment config
   */
  private updateShipmentConfig(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): void {
    if (updateDto.shipmentConfig !== undefined) {
      product.shipmentConfig = updateDto.shipmentConfig;
      this.logger.log(`Updated shipment config for product ${product.id}`);
    }
  }

  /**
   * Xử lý cập nhật inventory
   */
  private async processInventoryUpdate(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<InventoryResponseDto[]> {
    if (!updateDto.inventory) {
      return [];
    }

    this.logger.log(
      `Processing inventory update for PHYSICAL product ${product.id}`,
    );

    const inventoryResults: InventoryResponseDto[] = [];

    // Xử lý inventory data
    if (Array.isArray(updateDto.inventory)) {
      // Multiple inventory items
      for (const inventoryItem of updateDto.inventory) {
        const result = await this.updateSingleInventory(
          product.id,
          inventoryItem,
          userId,
        );
        if (result) {
          inventoryResults.push(result);
        }
      }
    } else {
      // Single inventory item
      const result = await this.updateSingleInventory(
        product.id,
        updateDto.inventory,
        userId,
      );
      if (result) {
        inventoryResults.push(result);
      }
    }

    return inventoryResults;
  }

  /**
   * Cập nhật một inventory item
   */
  private async updateSingleInventory(
    productId: number,
    inventoryData: ProductInventoryDto,
    _userId: number,
  ): Promise<InventoryResponseDto | null> {
    try {
      // Tìm inventory hiện có
      const existingInventory =
        await this.inventoryRepository.findByProductAndWarehouseNullable(
          productId,
          inventoryData.warehouseId || null,
        );

      let inventory: Inventory;

      if (existingInventory) {
        // Cập nhật inventory hiện có
        if (inventoryData.availableQuantity !== undefined) {
          existingInventory.availableQuantity = inventoryData.availableQuantity;
        }
        if (inventoryData.sku !== undefined) {
          existingInventory.sku = inventoryData.sku;
        }
        if (inventoryData.barcode !== undefined) {
          existingInventory.barcode = inventoryData.barcode;
        }

        // Tính toán lại số lượng
        this.validationHelper.calculateInventoryQuantities(existingInventory);
        existingInventory.lastUpdated = Date.now();

        inventory = await this.inventoryRepository.save(existingInventory);
      } else {
        // Tạo inventory mới
        const newInventory = new Inventory();
        newInventory.productId = productId;
        newInventory.warehouseId = inventoryData.warehouseId || null;
        newInventory.availableQuantity = inventoryData.availableQuantity || 0;
        newInventory.sku = inventoryData.sku || null;
        newInventory.barcode = inventoryData.barcode || null;

        // Tính toán số lượng
        this.validationHelper.calculateInventoryQuantities(newInventory);
        newInventory.lastUpdated = Date.now();

        inventory = await this.inventoryRepository.save(newInventory);
      }

      // Chuyển đổi sang DTO response
      const dto = new InventoryResponseDto();
      dto.id = inventory.id;
      dto.productId = inventory.productId;
      dto.warehouseId = inventory.warehouseId;
      dto.currentQuantity = inventory.currentQuantity;
      dto.totalQuantity = inventory.totalQuantity;
      dto.availableQuantity = inventory.availableQuantity;
      dto.reservedQuantity = inventory.reservedQuantity;
      dto.defectiveQuantity = inventory.defectiveQuantity;
      dto.lastUpdated = inventory.lastUpdated;
      dto.sku = inventory.sku;
      dto.barcode = inventory.barcode;

      // Lấy thông tin warehouse nếu có
      if (inventory.warehouseId) {
        const warehouse =
          await this.physicalWarehouseRepository.findByWarehouseIdWithDetails(
            inventory.warehouseId,
          );
        if (warehouse) {
          dto.warehouse = {
            id: warehouse.id,
            warehouseId: warehouse.warehouseId,
            name: warehouse.name,
            description: warehouse.description,
            type: warehouse.type,
            address: warehouse.address,
            capacity: warehouse.capacity,
          };
        }
      }

      return dto;
    } catch (error) {
      this.logger.error(
        `Error updating inventory for product ${productId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
