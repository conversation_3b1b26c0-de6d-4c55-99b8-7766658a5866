# HTTP Test Files for Product API

Bộ test HTTP files để kiểm tra tất cả các API liên quan đến sản phẩm trong hệ thống RedAI.

## 📁 Cấu trúc files

### 00-environment.http
- Thiết lập biến môi trường (baseUrl, token)
- Test authentication và health check
- Hướng dẫn sử dụng

### 01-physical-product.http
- Tạo sản phẩm vật lý với đầy đủ thông tin
- Test với inventory mới và có sẵn
- Test với warehouse cụ thể
- Test với giá phân loại
- Test với classifications

### 02-digital-product.http
- Tạo sản phẩm số với các delivery method khác nhau
- Test với EMAIL, SMS, DASHBOARD, AUTO_ACTIVE, ZALO, DIRECT_MESSAGE
- Test với output type khác nhau
- Test với giá phân loại và classifications

### 03-event-product.http
- Tạo sự kiện với ticket types
- Test workshop, hội thảo, webinar
- Test sự kiện miễn phí và có phí
- Test với multiple sessions

### 04-service-product.http
- Tạo dịch vụ với service packages
- Test dịch vụ thiết kế, tư vấn, chụp ảnh
- Test với giá theo giờ và theo gói
- Test với classifications

### 05-combo-product.http
- Tạo sản phẩm combo kết hợp nhiều sản phẩm
- Test combo khóa học, thiết bị, sách
- Test với số lượng khác nhau
- Test combo dịch vụ

### 06-batch-create-products.http
- Test tạo nhiều sản phẩm cùng lúc
- Test mixed types (Physical + Digital + Event)
- Test error cases trong batch
- Test giới hạn 50 sản phẩm

### 07-product-management.http
- Test CRUD operations
- Test get list với filters, sorting, pagination
- Test update với images, classifications
- Test inventory management
- Test bulk operations

### 08-error-cases.http
- Test tất cả các trường hợp lỗi
- Invalid data, missing fields
- Authentication errors
- Not found errors
- Validation errors

## 🚀 Cách sử dụng

### 1. Chuẩn bị
```bash
# Cài đặt VS Code extension: REST Client
# Hoặc sử dụng IntelliJ IDEA HTTP Client
```

### 2. Thiết lập environment
```http
# Trong file 00-environment.http
@baseUrl = http://localhost:3000/api/v1
@token = your_jwt_token_here
```

### 3. Chạy tests theo thứ tự
1. **00-environment.http** - Kiểm tra kết nối
2. **01-physical-product.http** - Test sản phẩm vật lý
3. **02-digital-product.http** - Test sản phẩm số
4. **03-event-product.http** - Test sự kiện
5. **04-service-product.http** - Test dịch vụ
6. **05-combo-product.http** - Test combo
7. **06-batch-create-products.http** - Test batch create
8. **07-product-management.http** - Test quản lý
9. **08-error-cases.http** - Test error cases

## 📋 Test Cases Coverage

### ✅ Product Types
- [x] PHYSICAL - Sản phẩm vật lý
- [x] DIGITAL - Sản phẩm số
- [x] EVENT - Sự kiện
- [x] SERVICE - Dịch vụ
- [x] COMBO - Sản phẩm combo

### ✅ Price Types
- [x] HAS_PRICE - Có giá cố định
- [x] CLASSIFICATION_PRICE - Giá theo phân loại

### ✅ Features
- [x] Basic product creation
- [x] With images upload URLs
- [x] With inventory management
- [x] With classifications
- [x] With custom fields
- [x] With warehouse selection
- [x] Batch operations
- [x] CRUD operations
- [x] Search and filters
- [x] Pagination and sorting

### ✅ Error Handling
- [x] Validation errors
- [x] Authentication errors
- [x] Not found errors
- [x] Business logic errors
- [x] Rate limiting
- [x] Invalid data formats

## 🔍 Expected Responses

### Success Response
```json
{
  "success": true,
  "message": "Tạo sản phẩm thành công",
  "data": {
    "id": 1,
    "name": "Áo thun nam",
    "productType": "PHYSICAL",
    "price": {
      "listPrice": 300000,
      "salePrice": 250000,
      "currency": "VND"
    },
    "uploadUrls": {
      "imagesUploadUrls": ["https://s3.amazonaws.com/..."]
    },
    "status": "PENDING",
    "createdAt": 1699123456789
  }
}
```

### Batch Success Response
```json
{
  "success": true,
  "message": "Tạo thành công 2/3 sản phẩm",
  "data": {
    "successProducts": [...],
    "failedProducts": [
      {
        "index": 2,
        "productName": "Sản phẩm lỗi",
        "error": "Tên sản phẩm đã tồn tại"
      }
    ],
    "totalProducts": 3,
    "successCount": 2,
    "failedCount": 1
  }
}
```

### Error Response
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    {
      "field": "name",
      "message": "Tên sản phẩm không được để trống"
    }
  ]
}
```

## 🛠 Debugging Tips

### 1. Check Authentication
```http
GET {{baseUrl}}/user/profile
Authorization: Bearer {{token}}
```

### 2. Check API Health
```http
GET {{baseUrl}}/health
```

### 3. Common Issues
- **401 Unauthorized**: Token hết hạn hoặc không hợp lệ
- **400 Bad Request**: Dữ liệu đầu vào không đúng format
- **422 Validation Error**: Thiếu trường bắt buộc hoặc dữ liệu không hợp lệ
- **500 Server Error**: Lỗi server, check logs

### 4. Response Headers
```
Content-Type: application/json
X-Request-ID: uuid-for-tracking
X-Rate-Limit-Remaining: 100
```

## 📊 Performance Testing

### Batch Create Performance
- Test với 1, 5, 10, 25, 50 sản phẩm
- Monitor response time và memory usage
- Expected: < 30s cho 50 sản phẩm

### Pagination Performance
- Test với limit 10, 50, 100
- Test với page lớn (page 100+)
- Expected: < 2s cho mọi request

## 🔐 Security Testing

### Authentication
- Test với token hết hạn
- Test với token không hợp lệ
- Test without token

### Authorization
- Test truy cập sản phẩm của user khác
- Test với role khác nhau

### Input Validation
- Test với SQL injection
- Test với XSS payload
- Test với file upload malicious

## 📈 Monitoring

### Metrics to Track
- Response time per endpoint
- Success/failure rate
- Error distribution
- Memory usage during batch operations
- Database query performance

### Alerts
- Response time > 5s
- Error rate > 5%
- Memory usage > 80%
- Database connection pool exhaustion
