import { Injectable, Logger } from '@nestjs/common';
import {
  ProductAdvancedInfoRepository,
} from '@modules/business/repositories';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { UserProduct } from '@modules/business/entities';
import { ValidationHelper } from '../../../helpers/validation.helper';
import { ClassificationService } from '../../classification.service';
import { CreateProductProcessor } from './create-product.processor';
import { CreateProductResult } from './create-product-orchestrator';
import { S3Service } from '@shared/services/s3.service';
import { ServiceProductCreateDto } from '../../../dto/request/create/service-product-create.dto';
import { CreateClassificationDto, ClassificationResponseDto } from '../../../dto/classification.dto';

/**
 * Processor chuyên xử lý logic tạo sản phẩm dịch vụ
 * <PERSON><PERSON><PERSON> tách từ UserProductService để tối ưu hóa và dễ maintain
 */
@Injectable()
export class ServiceProductProcessor {
  private readonly logger = new Logger(ServiceProductProcessor.name);

  constructor(
    private readonly createProcessor: CreateProductProcessor,
    private readonly productAdvancedInfoRepository: ProductAdvancedInfoRepository,
    private readonly validationHelper: ValidationHelper,
    private readonly classificationService: ClassificationService,
    private readonly s3Service: S3Service,
  ) {}

  /**
   * Tạo sản phẩm dịch vụ hoàn chỉnh
   */
  async createServiceProduct(
    dto: ServiceProductCreateDto,
    userId: number,
  ): Promise<CreateProductResult> {
    this.logger.log(`Creating SERVICE product: ${dto.name}`);

    // BƯỚC 1: Validate dữ liệu đầu vào cho sản phẩm dịch vụ
    await this.validateServiceProductData(dto);

    // BƯỚC 2: Xử lý service metadata và custom fields
    const serviceMetadata = this.processServiceMetadata(dto);
    const { customFields, metadata } = await this.createProcessor.processCustomFields(dto, serviceMetadata);

    // BƯỚC 3: Tạo sản phẩm cơ bản với shipment config = 0
    const product = await this.createProcessor.createBaseProduct(dto, userId, metadata);
    product.shipmentConfig = { widthCm: 0, heightCm: 0, lengthCm: 0, weightGram: 0 };

    // Service products luôn có giá cố định
    product.typePrice = dto.typePrice;
    product.price = dto.price;

    // BƯỚC 4: Lưu sản phẩm để có ID
    const savedProduct = await this.createProcessor.saveProduct(product);

    // BƯỚC 5: Xử lý hình ảnh sản phẩm chính
    const { imageEntries, imagesUploadUrls } = await this.createProcessor.processProductImages(dto, Date.now());

    // BƯỚC 6: Cập nhật sản phẩm với thông tin hình ảnh
    savedProduct.images = imageEntries;
    await this.createProcessor.saveProduct(savedProduct);

    // BƯỚC 7: Tạo advanced info (service packages)
    const advancedInfo = await this.createAdvancedInfo(savedProduct.id, dto.productType, dto, []);

    // BƯỚC 8: Xử lý hình ảnh cho service packages
    const advancedImagesUploadUrls = await this.createAdvancedImagesUploadUrls(dto, dto.productType, Date.now());

    // BƯỚC 9: Cập nhật detail_id để liên kết với advanced info
    if (advancedInfo?.id) {
      savedProduct.detail_id = advancedInfo.id;
      await this.createProcessor.saveProduct(savedProduct);

      // Cập nhật advanced info với image keys nếu có hình ảnh service packages
      if (advancedImagesUploadUrls.length > 0) {
        await this.updateAdvancedInfoWithImageKeys(advancedInfo.id, dto.productType, advancedImagesUploadUrls);
      }
    }

    // BƯỚC 10: Xử lý classifications (bao gồm service packages)
    const classifications = await this.processClassifications(savedProduct.id, dto, userId);

    // BƯỚC 11: Lấy sản phẩm cuối cùng
    const finalProduct = await this.createProcessor.getProductById(savedProduct.id);

    // BƯỚC 12: Tạo object chứa upload URLs cho cả sản phẩm chính và service packages
    const uploadUrls: any = {
      productId: finalProduct.id.toString(),
      imagesUploadUrls: imagesUploadUrls || []
    };

    // Thêm upload URLs cho hình ảnh service packages nếu có
    if (advancedImagesUploadUrls && advancedImagesUploadUrls.length > 0) {
      uploadUrls.advancedImagesUploadUrls = advancedImagesUploadUrls;
    }

    return {
      product: finalProduct,
      uploadUrls: (imagesUploadUrls.length > 0 || advancedImagesUploadUrls.length > 0) ? uploadUrls : null,
      additionalInfo: {
        advancedInfo,
        classifications
      }
    };
  }

  /**
   * Validate dữ liệu đầu vào cho sản phẩm dịch vụ
   */
  private async validateServiceProductData(dto: ServiceProductCreateDto): Promise<void> {
    // Validate service packages (optional nhưng nếu có thì phải hợp lệ)
    if (dto.servicePackages && dto.servicePackages.length > 0) {
      for (const servicePackage of dto.servicePackages) {
        if (!servicePackage.name) {
          throw new AppException(
            BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
            'Service package name is required',
          );
        }
        if (!servicePackage.price || servicePackage.price <= 0) {
          throw new AppException(
            BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
            'Service package price must be greater than 0',
          );
        }
        if (!servicePackage.duration || servicePackage.duration <= 0) {
          throw new AppException(
            BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
            'Service package duration must be greater than 0',
          );
        }
      }
    }

    // Kiểm tra price có tồn tại không (bắt buộc cho sản phẩm dịch vụ)
    if (!dto.price) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Price is required for service products',
      );
    }

    // Validate giá sản phẩm theo business rules
    this.validationHelper.validateProductPrice(dto.price, dto.typePrice, dto.productType);

    // Bỏ qua validation advanced info cho cấu trúc DTO mới
    // this.productValidationHelper.validateAdvancedInfo(dto.productType, dto.advancedInfo);

    this.logger.log(`Validated service product data for: ${dto.name}`);
  }

  /**
   * Xử lý service metadata đặc biệt cho dịch vụ
   */
  private processServiceMetadata(dto: ServiceProductCreateDto): any {
    this.logger.log(`Processing service metadata for: ${dto.name}`);

    // TODO: Implement logic từ service gốc
    // Tạo metadata đặc biệt cho dịch vụ
    
    return {
      serviceTime: dto.serviceTime || null,
      serviceDuration: dto.serviceDuration || null,
      serviceProvider: dto.serviceProvider || null,
      serviceType: dto.serviceType || null,
      serviceLocation: dto.serviceLocation || null,
      // ... other service metadata
    };
  }

  /**
   * Tạo advanced info cho sản phẩm dịch vụ
   */
  private async createAdvancedInfo(
    productId: number,
    productType: string,
    dto: ServiceProductCreateDto,
    additionalData: any[],
  ): Promise<any> {
    this.logger.log(`Creating advanced info for service product: ${productId}`);

    // Tạo advanced info cho sản phẩm dịch vụ với service packages
    return {
      id: Date.now(), // Temporary ID
      productId,
      productType,
      purchaseCount: dto.purchaseCount,
      servicePackages: dto.servicePackages,
    };
  }

  /**
   * Tạo upload URLs cho hình ảnh service packages
   */
  private async createAdvancedImagesUploadUrls(
    dto: ServiceProductCreateDto,
    productType: string,
    timestamp: number,
  ): Promise<any[]> {
    const uploadUrls: any[] = [];

    // Tạo presigned URLs cho hình ảnh service packages
    for (const servicePackage of dto.servicePackages) {
      if (servicePackage.imagesMediaTypes && servicePackage.imagesMediaTypes.length > 0) {
        for (let i = 0; i < servicePackage.imagesMediaTypes.length; i++) {
          const mediaType = servicePackage.imagesMediaTypes[i];
          const fileName = `service-package-${servicePackage.name}-image-${i}-${timestamp}`;

          // TODO: Implement actual S3 presigned URL generation
          uploadUrls.push({
            url: `https://presigned-url-example.com/${fileName}`,
            key: fileName,
            index: i,
            packageName: servicePackage.name
          });
        }
      }
    }

    this.logger.log(`Created ${uploadUrls.length} upload URLs for service package images`);
    return uploadUrls;
  }

  /**
   * Cập nhật advanced info với image keys
   */
  private async updateAdvancedInfoWithImageKeys(
    advancedInfoId: number,
    productType: string,
    advancedImagesUploadUrls: any[],
  ): Promise<void> {
    // TODO: Implement logic từ service gốc
    // Cập nhật advanced info với image keys nếu có hình ảnh service packages
    
    this.logger.log(`Updating advanced info with image keys for ${productType} product: ${advancedInfoId}`);
  }

  /**
   * Xử lý classifications cho sản phẩm (bao gồm cả service packages)
   */
  private async processClassifications(
    productId: number,
    dto: ServiceProductCreateDto,
    userId: number,
  ): Promise<ClassificationResponseDto[]> {
    const createdClassifications: ClassificationResponseDto[] = [];

    // 1. Xử lý classifications thông thường (nếu có)
    if (dto.classifications && dto.classifications.length > 0) {
      this.logger.log(`Processing ${dto.classifications.length} regular classifications for service product: ${productId}`);

      for (const classificationDto of dto.classifications) {
        try {
          const createdClassification = await this.classificationService.create(
            productId,
            classificationDto,
            userId,
          );
          createdClassifications.push(createdClassification);
          this.logger.log(`Created regular classification: ${createdClassification.id} for service product: ${productId}`);
        } catch (error) {
          this.logger.error(`Failed to create regular classification for service product ${productId}: ${error.message}`, error.stack);
          throw error;
        }
      }
    }

    // 2. Xử lý service packages như classifications
    if (dto.servicePackages && dto.servicePackages.length > 0) {
      this.logger.log(`Processing ${dto.servicePackages.length} service packages as classifications for service product: ${productId}`);

      for (const servicePackage of dto.servicePackages) {
        try {
          // Mapping ServicePackageDto sang CreateClassificationDto
          const classificationDto: CreateClassificationDto = {
            type: servicePackage.name,
            description: servicePackage.description,
            price: {
              listPrice: servicePackage.price,
              salePrice: servicePackage.price,
              currency: 'VND'
            },
            sku: `SERVICE-${servicePackage.name.replace(/\s+/g, '-').toUpperCase()}-${Date.now()}`,
            minQuantityPerPurchase: servicePackage.minQuantityPerPurchase || 1,
            maxQuantityPerPurchase: servicePackage.maxQuantityPerPurchase || 10,
            customFields: [],
            imagesMediaTypes: servicePackage.imagesMediaTypes || [],
            // Service package specific fields
            duration: servicePackage.duration,
            startTime: servicePackage.startTime,
            endTime: servicePackage.endTime,
            timezone: servicePackage.timezone,
            status: servicePackage.status,
            quantity: servicePackage.quantity,
          };

          const createdClassification = await this.classificationService.create(
            productId,
            classificationDto,
            userId,
          );
          createdClassifications.push(createdClassification);
          this.logger.log(`Created service package classification: ${createdClassification.id} for service product: ${productId}`);
        } catch (error) {
          this.logger.error(`Failed to create service package classification for service product ${productId}: ${error.message}`, error.stack);
          throw error;
        }
      }
    }

    this.logger.log(`Successfully created ${createdClassifications.length} total classifications for service product: ${productId}`);
    return createdClassifications;
  }
}
