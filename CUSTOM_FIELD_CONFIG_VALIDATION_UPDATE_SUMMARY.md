# Tóm tắt cập nhật Custom Field Config Validation

## Thay đổi đã thực hiện

### ✅ **1. T<PERSON><PERSON> hệ thống Interface và Validation**

#### **Enums**
- `src/modules/marketing/common/enums/custom-field-data-type.enum.ts` - Enum tập trung cho CustomFieldDataType

#### **Interfaces** 
- `src/modules/marketing/common/interfaces/custom-field-config.interface.ts`:
  - `TextCustomFieldConfig` - placeholder, defaultValue, pattern, minLength, maxLength
  - `NumberCustomFieldConfig` - placeholder, defaultValue, minValue, maxValue  
  - `BooleanCustomFieldConfig` - placeholder, defaultValue
  - `DateCustomFieldConfig` - placeholder, defaultValue
  - `SelectCustomFieldConfig` - placeholder, options[], defaultValue
  - `ObjectCustomFieldConfig` - placeholder, defaultValue

#### **Validation DTOs**
- `src/modules/marketing/common/dto/custom-field-config.dto.ts`:
  - DTOs với class-validator decorators cho từng loại config
  - `SelectOptionDto` cho options trong select type

#### **Utility Functions**
- `src/modules/marketing/common/utils/custom-field-config.util.ts`:
  - `validateConfig()` - validate config theo dataType
  - `validateValue()` - validate giá trị theo config  
  - `getDefaultConfig()` - tạo config mặc định
  - `getExampleConfig()` - lấy example config

#### **Examples**
- `src/modules/marketing/common/examples/custom-field-config.examples.ts`:
  - Basic examples cho từng dataType
  - Real-world examples (họ tên, email, địa chỉ, etc.)

### ✅ **2. Cập nhật Controllers với Validation**

#### **User Controller** (`src/modules/marketing/user/controllers/user-audience-custom-field-definition.controller.ts`):

**Create Method:**
```typescript
async create(@Body() createDto: CreateAudienceCustomFieldDefinitionDto) {
  // Validate config theo dataType nếu có config
  if (createDto.config && Object.keys(createDto.config).length > 0) {
    const configErrors = await CustomFieldConfigUtil.validateConfig(
      createDto.dataType,
      createDto.config
    );
    
    if (configErrors.length > 0) {
      throw new BadRequestException({
        message: 'Config không hợp lệ cho dataType đã chọn',
        errors: configErrors,
        dataType: createDto.dataType,
        receivedConfig: createDto.config
      });
    }
  } else {
    // Nếu không có config, sử dụng config mặc định
    createDto.config = CustomFieldConfigUtil.getDefaultConfig(createDto.dataType);
  }
  
  const result = await this.customFieldService.create(user.id, createDto);
  return wrapResponse(result, 'Tạo trường tùy chỉnh thành công');
}
```

**Update Method:**
```typescript
async update(@Body() updateDto: UpdateAudienceCustomFieldDefinitionDto) {
  // Nếu có cập nhật dataType hoặc config, cần validate
  if (updateDto.dataType || (updateDto.config && Object.keys(updateDto.config).length > 0)) {
    // Lấy thông tin hiện tại để biết dataType
    const currentField = await this.customFieldService.findOne(user.id, id);
    const dataTypeToValidate = updateDto.dataType || currentField.dataType;
    const configToValidate = updateDto.config || currentField.config;

    // Validate config theo dataType
    if (configToValidate && Object.keys(configToValidate).length > 0) {
      const configErrors = await CustomFieldConfigUtil.validateConfig(
        dataTypeToValidate,
        configToValidate
      );
      
      if (configErrors.length > 0) {
        throw new BadRequestException({
          message: 'Config không hợp lệ cho dataType đã chọn',
          errors: configErrors,
          dataType: dataTypeToValidate,
          receivedConfig: configToValidate
        });
      }
    }
  }

  const result = await this.customFieldService.update(user.id, id, updateDto);
  return wrapResponse(result, 'Cập nhật trường tùy chỉnh thành công');
}
```

**New Endpoint - Config Examples:**
```typescript
@Get('config-examples')
async getConfigExamples(): Promise<ApiResponseDto<Record<string, any>>> {
  const examples = {
    [CustomFieldDataType.TEXT]: CustomFieldConfigUtil.getExampleConfig(CustomFieldDataType.TEXT),
    [CustomFieldDataType.NUMBER]: CustomFieldConfigUtil.getExampleConfig(CustomFieldDataType.NUMBER),
    [CustomFieldDataType.BOOLEAN]: CustomFieldConfigUtil.getExampleConfig(CustomFieldDataType.BOOLEAN),
    [CustomFieldDataType.DATE]: CustomFieldConfigUtil.getExampleConfig(CustomFieldDataType.DATE),
    [CustomFieldDataType.SELECT]: CustomFieldConfigUtil.getExampleConfig(CustomFieldDataType.SELECT),
    [CustomFieldDataType.OBJECT]: CustomFieldConfigUtil.getExampleConfig(CustomFieldDataType.OBJECT),
  };
  
  return wrapResponse(examples, 'Lấy config examples thành công');
}
```

#### **Admin Controller** - Cập nhật tương tự với User Controller

### ✅ **3. Cấu trúc Config theo DataType**

#### **TEXT** (`dataType = 'text'`):
```json
{
  "placeholder": "Nhập họ tên...",
  "defaultValue": "",
  "pattern": "^[a-zA-ZÀ-ỹ\\s]+$",
  "minLength": 2,
  "maxLength": 100
}
```

#### **NUMBER** (`dataType = 'number'`):
```json
{
  "placeholder": "Nhập tuổi...",
  "defaultValue": 18,
  "minValue": 0,
  "maxValue": 120
}
```

#### **BOOLEAN** (`dataType = 'boolean'`):
```json
{
  "placeholder": "Đồng ý điều khoản",
  "defaultValue": false
}
```

#### **DATE** (`dataType = 'date'`):
```json
{
  "placeholder": "Chọn ngày sinh...",
  "defaultValue": "2000-01-01"
}
```

#### **SELECT** (`dataType = 'select'`):
```json
{
  "placeholder": "Chọn giới tính...",
  "options": [
    { "title": "Nam", "value": "male" },
    { "title": "Nữ", "value": "female" },
    { "title": "Khác", "value": "other" }
  ],
  "defaultValue": "male"
}
```

#### **OBJECT** (`dataType = 'object'`):
```json
{
  "placeholder": "Nhập thông tin địa chỉ...",
  "defaultValue": {
    "street": "",
    "city": "",
    "country": "Vietnam"
  }
}
```

### ✅ **4. API Endpoints mới**

#### **User Module:**
- `GET /user/marketing/audience-custom-fields/config-examples` - Lấy config examples

#### **Admin Module:**
- `GET /admin/marketing/audience-custom-fields/config-examples` - Lấy config examples

### ✅ **5. Validation Features**

1. **Automatic Config Validation**: Config được validate tự động theo dataType khi create/update
2. **Default Config**: Nếu không có config, hệ thống tự động tạo config mặc định
3. **Error Handling**: Trả về lỗi chi tiết khi config không hợp lệ
4. **Type Safety**: Sử dụng TypeScript interfaces để đảm bảo type safety
5. **Value Validation**: Utility để validate giá trị theo config

### ✅ **6. Documentation**

- `docs/CUSTOM_FIELD_CONFIG_INTERFACES.md` - Hướng dẫn sử dụng chi tiết
- Examples và best practices cho từng dataType

## Cách sử dụng

### **Frontend Integration:**

1. **Lấy config examples:**
```javascript
const response = await fetch('/user/marketing/audience-custom-fields/config-examples');
const examples = await response.json();
```

2. **Tạo custom field với config:**
```javascript
const customField = {
  fieldKey: 'full_name',
  displayName: 'Họ và tên',
  dataType: 'text',
  config: {
    placeholder: 'Nhập họ và tên đầy đủ...',
    minLength: 2,
    maxLength: 100,
    pattern: '^[a-zA-ZÀ-ỹ\\s]+$'
  }
};
```

3. **Error handling:**
```javascript
try {
  await createCustomField(customField);
} catch (error) {
  if (error.status === 400) {
    console.log('Config errors:', error.data.errors);
    console.log('DataType:', error.data.dataType);
    console.log('Received config:', error.data.receivedConfig);
  }
}
```

## Lợi ích

1. **Type Safety**: Đảm bảo config đúng cấu trúc theo dataType
2. **Validation**: Tự động validate config và giá trị
3. **User Experience**: Frontend có thể hiển thị form phù hợp với từng dataType
4. **Consistency**: Cấu trúc config nhất quán across toàn bộ hệ thống
5. **Extensibility**: Dễ dàng thêm dataType mới trong tương lai
