import { ApiProperty, ApiExtraModels } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { PriceTypeEnum, ProductTypeEnum, EntityStatusEnum } from '@modules/business/enums';
import { ClassificationResponseDto } from './classification.dto';
import { InventoryResponseDto } from './inventory';
import { DigitalAdvancedInfoDto } from './advanced-info/digital-advanced-info.dto';
import { EventAdvancedInfoDto } from './advanced-info/event-advanced-info.dto';
import { ServiceAdvancedInfoDto } from './advanced-info/service-advanced-info.dto';
import { ComboAdvancedInfoDto } from './advanced-info/combo-advanced-info.dto';

// Đã loại bỏ GridConfigDto vì không còn cần thiết

// Đã loại bỏ FieldConfigDto vì không còn cần thiết

// Đã loại bỏ FieldDto vì không còn cần thiết

// Đã loại bỏ GroupDto vì không còn cần thiết

// Đã loại bỏ CustomFieldResponseDto vì không còn cần thiết

/**
 * DTO cho cấu hình vận chuyển
 */
export class ShipmentConfigResponseDto {
  @Expose()
  @ApiProperty({
    description: 'Chiều rộng (cm)',
    example: 30,
  })
  widthCm: number;

  @Expose()
  @ApiProperty({
    description: 'Chiều cao (cm)',
    example: 5,
  })
  heightCm: number;

  @Expose()
  @ApiProperty({
    description: 'Chiều dài (cm)',
    example: 40,
  })
  lengthCm: number;

  @Expose()
  @ApiProperty({
    description: 'Trọng lượng (gram)',
    example: 250,
  })
  weightGram: number;
}

/**
 * DTO cho URL upload hình ảnh trong module business
 */
export class BusinessPresignedUrlImageDto {
  @ApiProperty({
    description: 'URL để upload hình ảnh',
    example: 'https://cdn.redai.vn/uploads/user_products/2025/05/product-image-0-1715270400000-uuid.jpg',
  })
  url: string;

  @ApiProperty({
    description: 'Key của hình ảnh trên S3',
    example: 'uploads/user_products/2025/05/product-image-0-1715270400000-uuid.jpg',
  })
  key: string;

  @ApiProperty({
    description: 'Chỉ số của hình ảnh',
    example: 0,
  })
  index: number;
}

/**
 * DTO cho thông tin URL upload trong module business
 */
export class BusinessUploadUrlsDto {
  @ApiProperty({
    description: 'ID sản phẩm',
    example: '101',
  })
  productId: string;

  @ApiProperty({
    description: 'Danh sách URL upload hình ảnh',
    type: [BusinessPresignedUrlImageDto],
  })
  imagesUploadUrls: BusinessPresignedUrlImageDto[];
}

/**
 * DTO cho response khi tạo sản phẩm thành công
 */
@ApiExtraModels(DigitalAdvancedInfoDto, EventAdvancedInfoDto, ServiceAdvancedInfoDto, ComboAdvancedInfoDto)
export class ProductResponseDto {
  @Expose()
  @ApiProperty({
    description: 'ID của sản phẩm',
    example: 101,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Áo thun nam',
  })
  name: string;

  @Expose()
  @ApiProperty({
    description: 'Loại sản phẩm',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.PHYSICAL,
  })
  productType: ProductTypeEnum;

  @Expose()
  @ApiProperty({
    description: 'Giá sản phẩm',
    oneOf: [
      {
        type: 'object',
        properties: {
          listPrice: { type: 'number', example: 200000 },
          salePrice: { type: 'number', example: 150000 },
          currency: { type: 'string', example: 'VND' }
        }
      },
      {
        type: 'object',
        properties: {
          priceDescription: { type: 'string', example: 'Giá chưa công bố' }
        }
      },
      { type: 'null' }
    ],
    example: { listPrice: 200000, salePrice: 150000, currency: 'VND' },
  })
  price: any;

  @Expose()
  @ApiProperty({
    description: 'Loại giá',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
  })
  typePrice: PriceTypeEnum;

  @Expose()
  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'Áo thun nam chất liệu cotton cao cấp',
    required: false,
  })
  description?: string;

  @Expose()
  @ApiProperty({
    description: 'Danh sách hình ảnh',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        key: { type: 'string', example: 'uploads/user_products/2025/05/product-image-0-1715270400000-uuid.jpg' },
        position: { type: 'number', example: 0 },
        url: { type: 'string', example: 'https://cdn.redai.vn/uploads/user_products/2025/05/product-image-0-1715270400000-uuid.jpg?t=1715270400000' }
      }
    },
    example: [
      {
        key: 'uploads/user_products/2025/05/product-image-0-1715270400000-uuid.jpg',
        position: 0,
        url: 'https://cdn.redai.vn/uploads/user_products/2025/05/product-image-0-1715270400000-uuid.jpg?t=1715270400000'
      }
    ],
    required: false,
  })
  images?: Array<{
    key: string;
    position: number;
    url: string;
  }>;

  @Expose()
  @ApiProperty({
    description: 'Danh sách tag',
    type: [String],
    example: ['áo thun', 'nam', 'cotton'],
    required: false,
  })
  tags?: string[];

  // Các trường groupFieldId và customFields đã bị loại bỏ vì không tồn tại trong database

  @Expose()
  @ApiProperty({
    description: 'ID người tạo',
    example: 1001,
  })
  createdBy: number;

  @Expose()
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1741708800000,
  })
  createdAt: number;

  @Expose()
  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1741708800000,
  })
  updatedAt: number;

  @Expose()
  @ApiProperty({
    description: 'Cấu hình vận chuyển',
    type: ShipmentConfigResponseDto,
  })
  @Type(() => ShipmentConfigResponseDto)
  shipmentConfig: ShipmentConfigResponseDto;

  @Expose()
  @ApiProperty({
    description: 'Trạng thái sản phẩm',
    enum: EntityStatusEnum,
    example: EntityStatusEnum.PENDING,
  })
  status: EntityStatusEnum;

  @Expose()
  @ApiProperty({
    description: 'ID của thông tin nâng cao (product_advanced_info)',
    example: 123,
    required: false,
  })
  detail_id?: number;

  @Expose()
  @ApiProperty({
    description: 'Metadata chứa custom fields và thông tin bổ sung. Đối với sản phẩm số (DIGITAL), có thể chứa thêm variant management. Đối với sản phẩm dịch vụ (SERVICE), có thể chứa thông tin dịch vụ.',
    example: {
      custom_fields: [
        {
          id: 5,
          configId: 'product_size',
          label: 'Kích thước sản phẩm',
          type: 'select',
          required: true,
          configJson: { options: ['XS', 'S', 'M', 'L', 'XL'] },
          tags: ['product'],
          value: { value: 'XL' }
        }
      ],
      // Cho sản phẩm số (DIGITAL) - variant management
      variants: [
        {
          name: 'Basic',
          sku: 'BASIC-001',
          availableQuantity: 1,
          minQuantityPerPurchase: 1,
          maxQuantityPerPurchase: 1,
          price: {
            listPrice: 500000,
            salePrice: 400000,
            currency: 'VND'
          },
          images: ['variant-0-image-0-1704067200000'],
          description: 'Phiên bản cơ bản',
          customFields: [
            {
              id: 15,
              configId: 'course_type',
              label: 'Loại khóa học',
              type: 'text',
              required: true,
              configJson: {},
              tags: ['product'],
              value: { value: 'Khóa học cơ bản' }
            }
          ]
        },
        {
          name: 'Pro',
          sku: 'PRO-001',
          availableQuantity: 1,
          minQuantityPerPurchase: 1,
          maxQuantityPerPurchase: 5,
          price: {
            listPrice: 1500000,
            salePrice: 1200000,
            currency: 'VND'
          },
          images: ['variant-1-image-0-1704067200000', 'variant-1-image-1-1704067200000'],
          description: 'Phiên bản chuyên nghiệp',
          customFields: [
            {
              id: 15,
              configId: 'course_type',
              label: 'Loại khóa học',
              type: 'text',
              required: true,
              configJson: {},
              tags: ['product'],
              value: { value: 'Khóa học chuyên nghiệp' }
            },
            {
              id: 16,
              configId: 'support_level',
              label: 'Mức độ hỗ trợ',
              type: 'select',
              required: false,
              configJson: { options: ['Basic', 'Premium', 'VIP'] },
              tags: ['product'],
              value: { value: 'Premium' }
            }
          ]
        }
      ],
      // Cho sản phẩm dịch vụ (SERVICE) - service information
      serviceTime: 1704067200000,
      serviceDuration: '60',
      serviceProvider: 'Công ty tư vấn ABC',
      serviceType: 'CONSULTATION',
      serviceLocation: 'AT_CENTER'
    },
    required: false,
  })
  metadata?: any;

  // Service-specific fields (exposed at root level for frontend convenience)
  @Expose()
  @ApiProperty({
    description: 'Thời gian thực hiện dịch vụ (timestamp) - Chỉ có cho sản phẩm SERVICE',
    example: 1704067200000,
    required: false,
  })
  serviceTime?: number;

  @Expose()
  @ApiProperty({
    description: 'Thời lượng dịch vụ (phút) - Chỉ có cho sản phẩm SERVICE',
    example: '60',
    required: false,
  })
  serviceDuration?: string;

  @Expose()
  @ApiProperty({
    description: 'Nhà cung cấp dịch vụ - Chỉ có cho sản phẩm SERVICE',
    example: 'Công ty tư vấn ABC',
    required: false,
  })
  serviceProvider?: string;

  @Expose()
  @ApiProperty({
    description: 'Loại dịch vụ - Chỉ có cho sản phẩm SERVICE',
    example: 'CONSULTATION',
    enum: ['CONSULTATION', 'TRAINING', 'SUPPORT', 'MAINTENANCE', 'OTHER'],
    required: false,
  })
  serviceType?: string;

  @Expose()
  @ApiProperty({
    description: 'Địa điểm thực hiện dịch vụ - Chỉ có cho sản phẩm SERVICE',
    example: 'AT_CENTER',
    enum: ['AT_CENTER', 'AT_CUSTOMER', 'ONLINE', 'HYBRID'],
    required: false,
  })
  serviceLocation?: string;

  // Đã loại bỏ các trường nameEmbedding, descriptionEmbedding, tagsEmbedding theo yêu cầu

  @Expose()
  @ApiProperty({
    description: 'Thông tin URL upload',
    type: BusinessUploadUrlsDto,
    required: false,
  })
  uploadUrls?: BusinessUploadUrlsDto;

  @Expose()
  @ApiProperty({
    description: 'Danh sách phân loại sản phẩm',
    type: [ClassificationResponseDto],
    required: false,
  })
  classifications?: ClassificationResponseDto[];

  @Expose()
  @ApiProperty({
    description: 'Thông tin tồn kho sản phẩm (nếu được tạo cùng với sản phẩm)',
    type: InventoryResponseDto,
    required: false,
  })
  @Type(() => InventoryResponseDto)
  inventory?: InventoryResponseDto;

  @Expose()
  @ApiProperty({
    description: 'Thông tin nâng cao cho sản phẩm (chỉ có cho DIGITAL, EVENT, SERVICE, COMBO)',
    oneOf: [
      { $ref: '#/components/schemas/DigitalAdvancedInfoDto' },
      { $ref: '#/components/schemas/EventAdvancedInfoDto' },
      { $ref: '#/components/schemas/ServiceAdvancedInfoDto' },
      { $ref: '#/components/schemas/ComboAdvancedInfoDto' }
    ],
    required: false,
  })
  advancedInfo?: DigitalAdvancedInfoDto | EventAdvancedInfoDto | ServiceAdvancedInfoDto | ComboAdvancedInfoDto;

  @Expose()
  @ApiProperty({
    description: 'Thông tin combo sản phẩm (chỉ có cho COMBO products) - Deprecated: Sử dụng advancedInfo.info thay thế',
    example: {
      info: [
        { productId: 123, total: 2 },
        { productId: 456, total: 1 }
      ]
    },
    required: false,
    deprecated: true,
  })
  combo?: { info: Array<{ productId: number; total: number }> };
}

/**
 * DTO cho response khi tạo nhiều sản phẩm thành công
 */
export class BatchProductResponseDto {
  @Expose()
  @ApiProperty({
    description: 'Danh sách sản phẩm đã tạo thành công',
    type: [ProductResponseDto],
  })
  @Type(() => ProductResponseDto)
  successProducts: ProductResponseDto[];

  @Expose()
  @ApiProperty({
    description: 'Danh sách sản phẩm tạo thất bại',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        index: { type: 'number', description: 'Vị trí trong mảng gốc' },
        productName: { type: 'string', description: 'Tên sản phẩm' },
        error: { type: 'string', description: 'Lỗi xảy ra' }
      }
    },
    example: [
      {
        index: 1,
        productName: 'Áo thun nữ',
        error: 'Tên sản phẩm đã tồn tại'
      }
    ]
  })
  failedProducts: Array<{
    index: number;
    productName: string;
    error: string;
  }>;

  @Expose()
  @ApiProperty({
    description: 'Tổng số sản phẩm được gửi',
    example: 5,
  })
  totalProducts: number;

  @Expose()
  @ApiProperty({
    description: 'Số sản phẩm tạo thành công',
    example: 4,
  })
  successCount: number;

  @Expose()
  @ApiProperty({
    description: 'Số sản phẩm tạo thất bại',
    example: 1,
  })
  failedCount: number;
}
