# Cập nhật API Admin Media - Thêm ownerType

## Tóm tắt thay đổi

API `GET /api/v1/admin/media` đã được cập nhật để:
1. **T<PERSON><PERSON> về field `ownerType`** trong response
2. **Hỗ trợ filter theo `ownerType`** trong query parameters

## Chi tiết thay đổi

### 1. MediaQueryDto (`src/modules/data/media/dto/media-query.dto.ts`)

**Thêm mới:**
```typescript
@ApiProperty({
    description: 'Loại chủ sở hữu media',
    enum: OwnerTypeEnum,
    required: false,
    example: OwnerTypeEnum.USER
})
@IsOptional()
@IsEnum(OwnerTypeEnum, {
    message: 'ownerType chỉ có thể là USER hoặc ADMIN'
})
ownerType?: OwnerTypeEnum;
```

### 2. AdminMediaResponseDto (`src/modules/data/media/dto/media-admin.dto.ts`)

**Thêm mới:**
```typescript
@ApiProperty({
    description: 'Loại chủ sở hữu media',
    enum: OwnerTypeEnum,
    example: OwnerTypeEnum.USER
})
declare ownerType: OwnerTypeEnum;
```

### 3. MediaRepository (`src/modules/data/media/repositories/media.repository.ts`)

**Cập nhật các method:**

#### `findAllForAdmin()`:
- Thêm `'media.ownerType'` vào select clause
- Thêm filter logic:
```typescript
if (query.ownerType) {
  queryBuilder.andWhere('media.ownerType = :ownerType', { ownerType: query.ownerType });
}
```

#### `findByIdConfig()`:
- Thêm `'media.ownerType'` vào select clause

#### `findByIds()` và `findAllUserMedia()`:
- Thêm `'media.ownerType'` vào select clause để consistency

## Cách sử dụng API

### 1. Lấy tất cả media (không filter)
```
GET /api/v1/admin/media?page=1&limit=10
```

### 2. Filter theo ownerType = USER
```
GET /api/v1/admin/media?page=1&limit=10&ownerType=USER
```

### 3. Filter theo ownerType = ADMIN
```
GET /api/v1/admin/media?page=1&limit=10&ownerType=ADMIN
```

### 4. Kết hợp nhiều filter
```
GET /api/v1/admin/media?page=1&limit=10&ownerType=USER&status=APPROVED&search=image
```

## Response Structure

```json
{
  "success": true,
  "message": "List of media retrieved successfully.",
  "result": {
    "items": [
      {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "name": "Sample Media",
        "description": "Sample description",
        "size": 1024000,
        "tags": ["tag1", "tag2"],
        "storageKey": "https://cdn.example.com/media/sample.jpg",
        "ownerType": "USER",
        "createdAt": 1625097600000,
        "updatedAt": 1625097600000,
        "status": "APPROVED",
        "author": "Nguyễn Văn A",
        "authorId": 1,
        "avatar": "https://cdn.example.com/avatar/user1.jpg"
      }
    ],
    "meta": {
      "totalItems": 1,
      "itemCount": 1,
      "itemsPerPage": 10,
      "totalPages": 1,
      "currentPage": 1
    }
  }
}
```

## Enum Values

```typescript
enum OwnerTypeEnum {
  USER = 'USER',
  ADMIN = 'ADMIN'
}
```

## Validation

- `ownerType` parameter chỉ chấp nhận giá trị `USER` hoặc `ADMIN`
- Nếu truyền giá trị khác sẽ trả về validation error
- Parameter là optional, không truyền sẽ lấy tất cả media

## Backward Compatibility

✅ **Hoàn toàn tương thích ngược**
- API cũ vẫn hoạt động bình thường
- Chỉ thêm field mới vào response
- Thêm query parameter optional mới

## Testing

Đã test các trường hợp:
- ✅ Lấy tất cả media (không filter)
- ✅ Filter theo ownerType = USER
- ✅ Filter theo ownerType = ADMIN  
- ✅ Kết hợp filter ownerType với status và search
- ✅ Validation ownerType enum
- ✅ Response structure đúng format
