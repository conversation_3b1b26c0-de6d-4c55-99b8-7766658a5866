import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  CustomFieldRepository,
  ProductAdvancedInfoRepository,
} from '@modules/business/repositories';
import { UserProduct } from '@modules/business/entities';
import { BusinessProductResponseDto as ProductResponseDto } from '../dto';
import { plainToInstance } from 'class-transformer';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { CdnService } from '@shared/services/cdn.service';
import { TimeIntervalEnum } from '@shared/utils';
import { ProductTypeEnum } from '@modules/business/enums';
import { EventFormatEnum } from '../dto';

@Injectable()
export class UserProductHelper {
  private readonly logger = new Logger(UserProductHelper.name);

  constructor(
    @InjectRepository(CustomFieldRepository)
    private readonly customFieldRepository: CustomFieldRepository,
    private readonly productAdvancedInfoRepository: ProductAdvancedInfoRepository,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Chuyển đổi từ entity sang DTO response
   * @param product Entity sản phẩm
   * @returns DTO response
   */
  async mapToProductResponseDto(
    product: UserProduct,
  ): Promise<ProductResponseDto> {
    try {
      // Chuyển đổi cơ bản
      const responseDto = plainToInstance(ProductResponseDto, product, {
        excludeExtraneousValues: true,
      });

      // Đảm bảo các trường quan trọng được map
      responseDto.status = product.status;
      responseDto.detail_id = product.detail_id || undefined;

      // Xử lý URL hình ảnh
      if (product.images && product.images.length > 0) {
        try {
          // Xử lý các trường hợp khác nhau của trường images với Promise.all
          responseDto.images = await Promise.all(
            product.images.map(async (img: any, index: number) => {
              let key = '';
              let position = index;

              // Trường hợp img là string
              if (typeof img === 'string') {
                key = img;
              }
              // Trường hợp img là object với key và position
              else if (img && typeof img === 'object') {
                if ('key' in img) {
                  key = img.key;
                }
                if ('position' in img && typeof img.position === 'number') {
                  position = img.position;
                }
              }

              // Kiểm tra key có hợp lệ không
              if (!key) {
                this.logger.warn(`Invalid image key found at position ${position}`);
                return {
                  key: '',
                  position: position,
                  url: ''
                };
              }

              // Sử dụng CDN signed URL như các module khác (marketplace, data/media)
              const url = this.cdnService.generateUrlView(key, TimeIntervalEnum.ONE_HOUR);
              const timestamp = Date.now();

              return {
                key: key,
                position: position,
                url: url ? `${url}?t=${timestamp}` : ''
              };
            })
          );
        } catch (error) {
          this.logger.error(`Lỗi khi xử lý hình ảnh: ${error.message}`, error.stack);
          responseDto.images = [];
        }
      }

      // Xử lý variant images trong metadata nếu có
      if (responseDto.metadata?.variants && Array.isArray(responseDto.metadata.variants)) {
        try {
          responseDto.metadata.variants = await Promise.all(
            responseDto.metadata.variants.map(async (variant: any) => {
              // Xử lý images của variant (chỉ xử lý nếu có images array)
              if (variant.images && Array.isArray(variant.images)) {
                const processedImages = await Promise.all(
                  variant.images.map(async (imageKey: string, index: number) => {
                    try {
                      // Tạo CDN URL cho variant image
                      const url = this.cdnService.generateUrlView(imageKey, TimeIntervalEnum.ONE_HOUR);
                      const timestamp = Date.now();

                      return {
                        key: imageKey,
                        position: index,
                        url: url ? `${url}?t=${timestamp}` : ''
                      };
                    } catch (error) {
                      this.logger.error(`Lỗi khi tạo URL cho variant image: ${error.message}`, error.stack);
                      return {
                        key: imageKey,
                        position: index,
                        url: ''
                      };
                    }
                  })
                );

                // Loại bỏ imageOperations khỏi response và trả về images đã xử lý
                const { imageOperations, ...variantWithoutOperations } = variant;
                return {
                  ...variantWithoutOperations,
                  images: processedImages
                };
              }

              // Loại bỏ imageOperations khỏi response nếu không có images
              const { imageOperations, ...variantWithoutOperations } = variant;
              return variantWithoutOperations;
            })
          );
        } catch (error) {
          this.logger.error(`Lỗi khi xử lý variant images: ${error.message}`, error.stack);
        }
      }

      // Xử lý thông tin nâng cao nếu sản phẩm không phải PHYSICAL
      if (product.productType !== ProductTypeEnum.PHYSICAL && product.advancedInfo) {
        try {
          const advancedInfo = product.advancedInfo;
          // Chuyển đổi thông tin nâng cao theo loại sản phẩm
          switch (product.productType) {
            case ProductTypeEnum.DIGITAL:
              responseDto.advancedInfo = {
                purchaseCount: advancedInfo.purchaseCount,
                digitalFulfillmentFlow: advancedInfo.digitalFulfillmentFlow ? {
                  deliveryMethod: advancedInfo.digitalFulfillmentFlow.deliveryMethod || 'email',
                  deliveryTiming: advancedInfo.digitalFulfillmentFlow.deliveryTiming || 'immediate',
                  deliveryDelayMinutes: advancedInfo.digitalFulfillmentFlow.delaySeconds ? Math.floor(advancedInfo.digitalFulfillmentFlow.delaySeconds / 60) : 0,
                  accessStatus: advancedInfo.digitalFulfillmentFlow.accessStatus || 'delivered'
                } : {
                  deliveryMethod: 'email',
                  deliveryTiming: 'immediate',
                  deliveryDelayMinutes: 0,
                  accessStatus: 'delivered'
                },
                digitalOutput: advancedInfo.digitalOutput ? {
                  outputType: advancedInfo.digitalOutput.outputType || 'online_course',
                  accessLink: advancedInfo.digitalOutput.accessLink || '',
                  loginInfo: advancedInfo.digitalOutput.loginInfo || null,
                  usageInstructions: advancedInfo.digitalOutput.usageInstructions || ''
                } : {
                  outputType: 'online_course',
                  accessLink: '',
                  loginInfo: null,
                  usageInstructions: ''
                },
                variantMetadata: product.metadata?.variantMetadata || {
                  variants: []
                }
              };
              break;

            case ProductTypeEnum.EVENT:
              responseDto.advancedInfo = {
                purchaseCount: advancedInfo.purchaseCount,
                eventFormat: advancedInfo.eventFormat as EventFormatEnum,
                eventLink: advancedInfo.eventLink,
                eventLocation: advancedInfo.eventLocation,
                startDate: advancedInfo.startDate,
                endDate: advancedInfo.endDate,
                timezone: advancedInfo.timezone,
                ticketTypes: await this.processTicketTypesForResponse(advancedInfo.ticketTypes || [], advancedInfo.images),
              };
              break;

            case ProductTypeEnum.SERVICE:
              responseDto.advancedInfo = {
                purchaseCount: advancedInfo.purchaseCount,
                servicePackages: await this.processServicePackagesForResponse(advancedInfo.servicePackages || [], advancedInfo.images),
              };
              break;

            case ProductTypeEnum.COMBO:
              responseDto.advancedInfo = {
                purchaseCount: advancedInfo.purchaseCount,
                info: advancedInfo.combo?.info || []
              };
              break;
          }
        } catch (error) {
          this.logger.warn(`Không thể xử lý thông tin nâng cao cho sản phẩm ${product.id}: ${error.message}`);
        }
      }

      // Xử lý service fields từ metadata cho sản phẩm SERVICE
      if (product.productType === ProductTypeEnum.SERVICE && product.metadata) {
        try {
          // Thêm service fields vào response để frontend dễ sử dụng
          if (product.metadata.serviceTime !== undefined) {
            responseDto['serviceTime'] = product.metadata.serviceTime;
          }
          if (product.metadata.serviceDuration !== undefined) {
            responseDto['serviceDuration'] = product.metadata.serviceDuration;
          }
          if (product.metadata.serviceProvider !== undefined) {
            responseDto['serviceProvider'] = product.metadata.serviceProvider;
          }
          if (product.metadata.serviceType !== undefined) {
            responseDto['serviceType'] = product.metadata.serviceType;
          }
          if (product.metadata.serviceLocation !== undefined) {
            responseDto['serviceLocation'] = product.metadata.serviceLocation;
          }
        } catch (error) {
          this.logger.warn(`Không thể xử lý service fields cho sản phẩm ${product.id}: ${error.message}`);
        }
      }

      // Đã loại bỏ xử lý các trường tùy chỉnh vì không tồn tại trong database

      return responseDto;
    } catch (error) {
      this.logger.error(`Lỗi khi chuyển đổi sản phẩm sang DTO: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_MAPPING_FAILED,
        `Lỗi khi chuyển đổi sản phẩm sang DTO: ${error.message}`,
      );
    }
  }

  /**
   * Xử lý ticket types để trả về response với images
   * @param ticketTypes Danh sách ticket types từ database
   * @param advancedImages Danh sách images từ advanced info
   * @returns Ticket types với images đã được xử lý
   */
  private async processTicketTypesForResponse(ticketTypes: any[], advancedImages: any[]): Promise<any[]> {
    if (!ticketTypes || !Array.isArray(ticketTypes)) {
      return [];
    }

    return Promise.all(
      ticketTypes.map(async (ticket, ticketIndex) => {
        // Xử lý images từ ticket.images nếu có
        let processedImages: any[] = [];

        if (ticket.images && Array.isArray(ticket.images)) {
          processedImages = await Promise.all(
            ticket.images.map(async (img: any) => {
              try {
                // Tạo CDN URL cho image
                const url = this.cdnService.generateUrlView(img.key, TimeIntervalEnum.ONE_HOUR);
                const timestamp = Date.now();

                return {
                  key: img.key,
                  position: img.position || 0,
                  url: url ? `${url}?t=${timestamp}` : ''
                };
              } catch (error) {
                this.logger.error(`Lỗi khi tạo URL cho ticket image: ${error.message}`, error.stack);
                return {
                  key: img.key || '',
                  position: img.position || 0,
                  url: ''
                };
              }
            })
          );
        }

        // Loại bỏ field 'images' gốc và thêm images đã được xử lý
        const { images, ...ticketWithoutImages } = ticket;

        return {
          ...ticketWithoutImages,
          images: processedImages
        };
      })
    );
  }

  /**
   * Xử lý service packages để trả về response với images
   * @param servicePackages Danh sách service packages từ database
   * @param advancedImages Danh sách images từ advanced info
   * @returns Service packages với images đã được xử lý
   */
  private async processServicePackagesForResponse(servicePackages: any[], advancedImages: any[]): Promise<any[]> {
    if (!servicePackages || !Array.isArray(servicePackages)) {
      return [];
    }

    return Promise.all(
      servicePackages.map(async (pkg, packageIndex) => {
        // Xử lý images từ pkg.images nếu có
        let processedImages: any[] = [];

        if (pkg.images && Array.isArray(pkg.images)) {
          processedImages = await Promise.all(
            pkg.images.map(async (img: any) => {
              try {
                // Tạo CDN URL cho image
                const url = this.cdnService.generateUrlView(img.key, TimeIntervalEnum.ONE_HOUR);
                const timestamp = Date.now();

                return {
                  key: img.key,
                  position: img.position || 0,
                  url: url ? `${url}?t=${timestamp}` : ''
                };
              } catch (error) {
                this.logger.error(`Lỗi khi tạo URL cho service package image: ${error.message}`, error.stack);
                return {
                  key: img.key || '',
                  position: img.position || 0,
                  url: ''
                };
              }
            })
          );
        }

        // Loại bỏ field 'images' gốc và thêm images đã được xử lý
        const { images, ...packageWithoutImages } = pkg;

        return {
          ...packageWithoutImages,
          images: processedImages
        };
      })
    );
  }

  /**
   * Xử lý images cho advanced info (ticket types hoặc service packages)
   * @param advancedImages Danh sách images từ advanced info
   * @param type Loại: 'ticket' hoặc 'service'
   * @param index Index của item
   * @returns Danh sách images với key và URL
   */
  private async processAdvancedImages(advancedImages: any[], type: string, index: number): Promise<any[]> {
    if (!advancedImages || !Array.isArray(advancedImages)) {
      this.logger.debug(`No advanced images found for type: ${type}, index: ${index}`);
      return [];
    }

    this.logger.debug(`Processing advanced images for type: ${type}, index: ${index}, total images: ${advancedImages.length}`);

    // Lọc images theo type và index
    const filteredImages = advancedImages.filter(img =>
      img.type === type && img.index === index
    );

    this.logger.debug(`Filtered ${filteredImages.length} images for type: ${type}, index: ${index}`);

    // Xử lý từng image để tạo URL
    return Promise.all(
      filteredImages.map(async (img: any) => {
        try {
          // Sử dụng CDN signed URL như các module khác
          const url = this.cdnService.generateUrlView(img.key, TimeIntervalEnum.ONE_HOUR);
          const timestamp = Date.now();

          const result = {
            key: img.key,
            position: img.position || 0,
            url: url ? `${url}?t=${timestamp}` : ''
          };

          this.logger.debug(`Generated URL for advanced image: ${JSON.stringify(result)}`);
          return result;
        } catch (error) {
          this.logger.error(`Lỗi khi tạo URL cho advanced image: ${error.message}`, error.stack);
          return {
            key: img.key || '',
            position: img.position || 0,
            url: ''
          };
        }
      })
    );
  }
}