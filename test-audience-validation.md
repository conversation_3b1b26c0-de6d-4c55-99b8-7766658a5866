# Test Audience Email & Phone Validation

## Mô tả
Test validation cho email và phone không được trùng lặp khi tạo và cập nhật audience.

## Test Cases

### 1. Test Create Audience - Email Duplicate
```bash
# Tạo audience đầu tiên
POST /marketing/audiences
{
  "name": "Kh<PERSON>ch hàng A",
  "email": "<EMAIL>",
  "phone": "0123456789"
}
# Expected: 201 Created

# Tạo audience thứ hai với email trùng lặp
POST /marketing/audiences
{
  "name": "<PERSON>h<PERSON>ch hàng B", 
  "email": "<EMAIL>",
  "phone": "0987654321"
}
# Expected: 409 Conflict
# Error Code: 13014
# Message: "Email <EMAIL> đã được sử dụng bởi khách hàng khác"
```

### 2. Test Create Audience - Phone Duplicate
```bash
# Tạo audience với phone trùng lặp
POST /marketing/audiences
{
  "name": "<PERSON>h<PERSON>ch hàng C",
  "email": "<EMAIL>", 
  "phone": "0123456789"
}
# Expected: 409 Conflict
# Error Code: 13015
# Message: "Số điện thoại 0123456789 đã được sử dụng bởi khách hàng khác"
```

### 3. Test Update Audience - Email Duplicate
```bash
# Cập nhật audience với email đã tồn tại
PUT /marketing/audiences/2
{
  "email": "<EMAIL>"
}
# Expected: 409 Conflict
# Error Code: 13014
# Message: "Email <EMAIL> đã được sử dụng bởi khách hàng khác"
```

### 4. Test Update Audience - Phone Duplicate
```bash
# Cập nhật audience với phone đã tồn tại
PUT /marketing/audiences/2
{
  "phone": "0123456789"
}
# Expected: 409 Conflict
# Error Code: 13015
# Message: "Số điện thoại 0123456789 đã được sử dụng bởi khách hàng khác"
```

### 5. Test Update Audience - Same Email/Phone (Should Pass)
```bash
# Cập nhật audience với chính email/phone của nó
PUT /marketing/audiences/1
{
  "name": "Khách hàng A Updated",
  "email": "<EMAIL>",
  "phone": "0123456789"
}
# Expected: 200 OK (không có lỗi vì đây là chính audience đó)
```

### 6. Test Create Audience - Empty Email/Phone (Should Pass)
```bash
# Tạo audience với email/phone rỗng
POST /marketing/audiences
{
  "name": "Khách hàng D"
}
# Expected: 201 Created

POST /marketing/audiences
{
  "name": "Khách hàng E",
  "email": "",
  "phone": ""
}
# Expected: 201 Created
```

## Error Response Format
```json
{
  "code": 13014,
  "message": "Email <EMAIL> đã được sử dụng bởi khách hàng khác",
  "result": null
}
```

## Notes
- Validation chỉ áp dụng khi email/phone không rỗng và không null
- Validation trim() whitespace trước khi kiểm tra
- Update validation loại trừ chính audience đang được cập nhật
- Validation scope theo userId (chỉ kiểm tra trong audience của cùng user)
