# Update Product Processors

## Tổng quan

Th<PERSON> mục này chứa các processor đư<PERSON>c bóc tách từ method `updateProduct` trong `UserProductService` để tối ưu hóa code và dễ maintain hơn.

## Cấu trúc

### 1. UpdateProductProcessor (`update-product.processor.ts`)
**Chức năng**: <PERSON><PERSON> lý các logic nghiệp vụ cụ thể cho việc cập nhật sản phẩm

**Các method chính**:
- `findAndValidateProduct()` - Tìm và validate sản phẩm
- `updateBasicFields()` - Cập nhật thông tin cơ bản
- `updateProductPricing()` - Xử lý cập nhật giá
- `updateCustomFields()` - X<PERSON> lý custom fields và metadata
- `processAdvancedInfoUpdate()` - <PERSON><PERSON> lý advanced info
- `processClassificationsUpdate()` - <PERSON><PERSON> lý classifications
- `processInventoryUpdate()` - <PERSON><PERSON> lý inventory
- `buildUpdateResponse()` - <PERSON><PERSON><PERSON> dựng response cuối cùng

### 2. UpdateProductOrchestrator (`update-product-orchestrator.ts`)
**Chức năng**: Điều phối toàn bộ luồng update, gọi các processor theo đúng thứ tự

**Method chính**:
- `updateProduct()` - Method chính thay thế cho method cũ

**Luồng xử lý**:
1. Validate sản phẩm
2. Cập nhật thông tin cơ bản
3. Cập nhật giá
4. Xử lý hình ảnh chính
5. Cập nhật custom fields
6. Lưu sản phẩm
7. Xử lý advanced info
8. Xử lý image operations
9. Xử lý classifications
10. Xử lý inventory
11. Xây dựng response

## Cách sử dụng

### 1. Trong UserProductService

```typescript
import { UpdateProductOrchestrator } from './processors';

@Injectable()
export class UserProductService {
  constructor(
    private readonly updateProductOrchestrator: UpdateProductOrchestrator,
    // ... other dependencies
  ) {}

  @Transactional()
  async updateProduct(
    id: number,
    updateProductDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<ProductResponseDto> {
    return await this.updateProductOrchestrator.updateProduct(id, updateProductDto, userId);
  }
}
```

### 2. Đăng ký trong Module

```typescript
import { UpdateProductProcessor, UpdateProductOrchestrator } from './services/processors';

@Module({
  providers: [
    UserProductService,
    UpdateProductProcessor,
    UpdateProductOrchestrator,
    // ... other providers
  ],
})
export class UserModule {}
```

## Lợi ích

### 1. **Tách biệt trách nhiệm**
- Mỗi processor chỉ xử lý một nhóm logic cụ thể
- Orchestrator chỉ điều phối luồng, không chứa business logic

### 2. **Dễ test**
- Có thể test từng processor riêng biệt
- Mock dependencies dễ dàng hơn

### 3. **Dễ maintain**
- Code ngắn gọn, dễ đọc
- Dễ thêm/sửa logic cho từng bước

### 4. **Tái sử dụng**
- Các processor có thể được sử dụng ở nơi khác
- Logic có thể được extend dễ dàng

## Cần hoàn thiện

### 1. Implement các placeholder methods trong UpdateProductProcessor:
- `createAdvancedImagesUploadUrls()`
- `updateAdvancedInfo()`
- `updateAdvancedInfoWithImageKeys()`
- `useExistingInventory()`
- `createOrUpdateProductInventory()`
- `processTicketTypesWithImages()`
- `processServicePackagesWithImages()`

### 2. Implement logic xử lý hình ảnh trong UpdateProductOrchestrator:
- `processMainProductImages()`
- `processAdvancedImageOperations()`

### 3. Thêm error handling và logging chi tiết hơn

### 4. Thêm validation và business rules

## Migration Plan

### Phase 1: Tạo processors (✅ Hoàn thành)
- Tạo UpdateProductProcessor
- Tạo UpdateProductOrchestrator
- Định nghĩa interfaces

### Phase 2: Implement placeholder methods
- Copy logic từ UserProductService gốc
- Adapt cho processor pattern

### Phase 3: Integration
- Đăng ký trong module
- Update UserProductService để sử dụng orchestrator
- Test integration

### Phase 4: Cleanup
- Remove method cũ từ UserProductService
- Refactor related code
- Update documentation

## Testing Strategy

### 1. Unit Tests
- Test từng method trong UpdateProductProcessor
- Mock tất cả dependencies

### 2. Integration Tests
- Test UpdateProductOrchestrator với real dependencies
- Test luồng hoàn chỉnh

### 3. E2E Tests
- Test API endpoint với processor mới
- So sánh kết quả với implementation cũ

## Performance Considerations

### 1. **Parallel Processing**
- Các operations độc lập có thể chạy song song
- Ví dụ: xử lý images và classifications

### 2. **Database Optimization**
- Batch operations khi có thể
- Minimize database calls

### 3. **Memory Management**
- Cleanup temporary data
- Avoid memory leaks trong long-running operations

## Error Handling Strategy

### 1. **Graceful Degradation**
- Các lỗi không critical chỉ log warning
- Tiếp tục xử lý các bước khác

### 2. **Transaction Management**
- Rollback khi có lỗi critical
- Maintain data consistency

### 3. **Error Reporting**
- Detailed error messages
- Error tracking và monitoring
