# Tóm tắt cập nhật CustomFieldDataType Enum

## Thay đổi đã thực hiện

### 1. Cập nhật Enum CustomFieldDataType

**Trước:**
```typescript
export enum CustomFieldDataType {
  STRING = 'string',
  INTEGER = 'integer',
  DATE = 'date',
  BOOLEAN = 'boolean',
  JSON = 'json',
}
```

**Sau:**
```typescript
export enum CustomFieldDataType {
  TEXT = 'text',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  DATE = 'date',
  SELECT = 'select',
  OBJECT = 'object',
}
```

### 2. Files đã cập nhật

#### DTOs
- ✅ `src/modules/marketing/user/dto/audience-custom-field-definition/create-audience-custom-field-definition.dto.ts`
- ✅ `src/modules/marketing/user/dto/audience-custom-field-definition/update-audience-custom-field-definition.dto.ts`
- ✅ `src/modules/marketing/user/dto/audience-custom-field-definition/audience-custom-field-definition-query.dto.ts`
- ✅ `src/modules/marketing/user/dto/audience-custom-field-definition/audience-custom-field-definition-response.dto.ts`
- ✅ `src/modules/marketing/admin/dto/audience-custom-field-definition/create-audience-custom-field-definition.dto.ts`
- ✅ `src/modules/marketing/admin/dto/audience-custom-field-definition/update-audience-custom-field-definition.dto.ts`
- ✅ `src/modules/marketing/admin/dto/audience-custom-field-definition/audience-custom-field-definition-query.dto.ts`
- ✅ `src/modules/marketing/admin/dto/audience-custom-field-definition/audience-custom-field-definition-response.dto.ts`

#### Entities
- ✅ `src/modules/marketing/user/entities/user-audience-custom-field-definition.entity.ts`
- ✅ `src/modules/marketing/admin/entities/admin-audience-custom-field-definition.entity.ts`

#### Services
- ✅ `src/modules/marketing/user/services/user-audience-custom-field-definition.service.ts`
- ✅ `src/modules/marketing/admin/services/admin-audience-custom-field-definition.service.ts`

### 3. Database Migration

#### Migration Files
- ✅ `src/database/migrations/1720000000006-UpdateCustomFieldDataTypeToEnum.ts`
- ✅ `src/database/migrations/update-custom-field-data-type-to-enum.sql`
- ✅ `src/database/migrations/rollback-custom-field-data-type-enum.sql`

#### Scripts
- ✅ `scripts/run-custom-field-data-type-migration.sh`
- ✅ `scripts/run-custom-field-data-type-migration.ps1`

#### Documentation
- ✅ `docs/CUSTOM_FIELD_DATA_TYPE_ENUM_MIGRATION.md`

## Mapping giá trị

| Giá trị cũ | Giá trị mới | Mô tả |
|------------|-------------|-------|
| `string`   | `text`      | Văn bản |
| `integer`  | `number`    | Số |
| `boolean`  | `boolean`   | Boolean (không đổi) |
| `date`     | `date`      | Ngày tháng (không đổi) |
| `json`     | `object`    | Object/JSON |
| -          | `select`    | Dropdown/Select (mới) |

## Cách chạy Migration

### 1. Sử dụng Script (Khuyến nghị)
```bash
# Linux/macOS
chmod +x scripts/run-custom-field-data-type-migration.sh
./scripts/run-custom-field-data-type-migration.sh

# Windows PowerShell
.\scripts\run-custom-field-data-type-migration.ps1
```

### 2. Manual SQL
```bash
psql -h $DB_HOST -d $DB_NAME -U $DB_USER -f src/database/migrations/update-custom-field-data-type-to-enum.sql
```

### 3. TypeORM Migration
```bash
npm run migration:run
```

## Rollback (nếu cần)
```bash
psql -h $DB_HOST -d $DB_NAME -U $DB_USER -f src/database/migrations/rollback-custom-field-data-type-enum.sql
```

## Kiểm tra sau Migration

### 1. Kiểm tra Enum đã được tạo
```sql
SELECT t.typname, e.enumlabel
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid  
WHERE t.typname = 'custom_field_data_type_enum'
ORDER BY e.enumsortorder;
```

### 2. Kiểm tra cột đã được cập nhật
```sql
SELECT column_name, data_type, udt_name
FROM information_schema.columns 
WHERE table_name IN ('audience_user_custom_fields', 'audience_admin_custom_fields')
AND column_name = 'data_type';
```

## Lưu ý quan trọng

1. **Backup Database**: Luôn backup database trước khi chạy migration
2. **API Compatibility**: Frontend cần cập nhật để sử dụng enum values mới
3. **Testing**: Test kỹ các API endpoints sau khi migration
4. **Documentation**: Cập nhật API documentation nếu cần

## Tác động đến Frontend

Frontend cần cập nhật để sử dụng các giá trị enum mới:
- `'string'` → `'text'`
- `'integer'` → `'number'`
- `'json'` → `'object'`
- Thêm support cho `'select'` type mới
