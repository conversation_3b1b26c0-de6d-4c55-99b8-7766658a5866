import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import {
  UserProductRepository,
  CustomFieldRepository,
  ProductAdvancedInfoRepository,
} from '@modules/business/repositories';
import { BusinessUpdateProductDto } from '../../../dto';
import { ProductTypeEnum } from '@modules/business/enums';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { UserProduct } from '@modules/business/entities';
import { MetadataHelper } from '../../../helpers/metadata.helper';
import { UpdateProductResult } from './update-product-orchestrator';

/**
 * Processor chuyên xử lý cập nhật sản phẩm số (DIGITAL)
 * Xử lý delivery method, output type, classifications, variants
 */
@Injectable()
export class DigitalProductUpdateProcessor {
  private readonly logger = new Logger(DigitalProductUpdateProcessor.name);

  constructor(
    private readonly userProductRepository: UserProductRepository,
    private readonly customFieldRepository: CustomFieldRepository,
    private readonly productAdvancedInfoRepository: ProductAdvancedInfoRepository,
    private readonly metadataHelper: MetadataHelper,
  ) {}

  /**
   * Cập nhật sản phẩm số hoàn chỉnh
   */
  @Transactional()
  async updateDigitalProduct(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<UpdateProductResult> {
    this.logger.log(
      `Updating DIGITAL product: ${product.name} (ID: ${product.id})`,
    );

    // BƯỚC 1: Validate dữ liệu đầu vào cho sản phẩm số
    await this.validateDigitalProductData(updateDto);

    // BƯỚC 2: Cập nhật digital-specific fields
    this.updateDigitalFields(product, updateDto);

    // BƯỚC 3: Xử lý variant metadata
    await this.processVariantMetadata(product, updateDto);

    // BƯỚC 4: Đảm bảo shipment config = 0 cho sản phẩm số
    this.ensureZeroShipmentConfig(product);

    // BƯỚC 5: Lưu sản phẩm đã cập nhật
    const updatedProduct = await this.userProductRepository.save(product);

    // BƯỚC 6: Xử lý advanced info nếu có
    await this.processAdvancedInfoUpdate(updatedProduct, updateDto);

    return {
      product: updatedProduct,
      imagesUploadUrls: [],
      advancedImagesUploadUrls: [],
      classificationUploadUrls: [],
      classifications: [],
      inventory: null, // Digital products don't have inventory
    };
  }

  /**
   * Validate dữ liệu đầu vào cho sản phẩm số
   */
  private async validateDigitalProductData(
    updateDto: BusinessUpdateProductDto,
  ): Promise<void> {
    // Validate delivery method nếu có
    if (updateDto.deliveryMethod) {
      const validDeliveryMethods = [
        'DASHBOARD',
        'EMAIL',
        'SMS',
        'DIRECT_MESSAGE',
        'ZALO',
        'AUTO_ACTIVE',
      ];
      if (!validDeliveryMethods.includes(updateDto.deliveryMethod)) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          `Delivery method không hợp lệ: ${updateDto.deliveryMethod}`,
        );
      }
    }

    // Validate delivery timing nếu có
    if (updateDto.deliveryTiming) {
      const validDeliveryTimings = ['IMMEDIATE', 'DELAYED'];
      if (!validDeliveryTimings.includes(updateDto.deliveryTiming)) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          `Delivery timing không hợp lệ: ${updateDto.deliveryTiming}`,
        );
      }
    }

    // Validate output type nếu có
    if (updateDto.outputType) {
      const validOutputTypes = [
        'DOWNLOAD_LINK',
        'ACCESS_CODE',
        'ACCOUNT_INFO',
        'CONTENT',
      ];
      if (!validOutputTypes.includes(updateDto.outputType)) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          `Output type không hợp lệ: ${updateDto.outputType}`,
        );
      }
    }

    // Validate purchase count nếu có
    if (updateDto.purchaseCount !== undefined && updateDto.purchaseCount < 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Purchase count không thể âm',
      );
    }
  }

  /**
   * Cập nhật các trường đặc thù cho sản phẩm số
   */
  private updateDigitalFields(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): void {
    // Cập nhật delivery method
    if (updateDto.deliveryMethod !== undefined) {
      product.metadata = product.metadata || {};
      product.metadata.deliveryMethod = updateDto.deliveryMethod;
      this.logger.log(
        `Updated delivery method to: ${updateDto.deliveryMethod}`,
      );
    }

    // Cập nhật delivery timing
    if (updateDto.deliveryTiming !== undefined) {
      product.metadata = product.metadata || {};
      product.metadata.deliveryTiming = updateDto.deliveryTiming;
      this.logger.log(
        `Updated delivery timing to: ${updateDto.deliveryTiming}`,
      );
    }

    // Cập nhật output type
    if (updateDto.outputType !== undefined) {
      product.metadata = product.metadata || {};
      product.metadata.outputType = updateDto.outputType;
      this.logger.log(`Updated output type to: ${updateDto.outputType}`);
    }

    // Cập nhật purchase count
    if (updateDto.purchaseCount !== undefined) {
      product.metadata = product.metadata || {};
      product.metadata.purchaseCount = updateDto.purchaseCount;
      this.logger.log(`Updated purchase count to: ${updateDto.purchaseCount}`);
    }
  }

  /**
   * Xử lý variant metadata cho sản phẩm số
   */
  private async processVariantMetadata(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): Promise<void> {
    const digitalAdvancedInfo = updateDto.advancedInfo as any;

    if (digitalAdvancedInfo?.variantMetadata?.variants) {
      this.logger.log(
        `Processing variant metadata with ${digitalAdvancedInfo.variantMetadata.variants.length} variants`,
      );

      // Xử lý từng variant
      const processedVariants = await Promise.all(
        digitalAdvancedInfo.variantMetadata.variants.map(
          async (variant: any, index: number) => {
            // Loại bỏ imagesMediaTypes khỏi variant để tránh lưu vào metadata
            const { imagesMediaTypes, ...variantWithoutImages } = variant;

            // Xử lý custom fields cho variant nếu có
            if (variant.customFields && variant.customFields.length > 0) {
              const customFieldIds = this.metadataHelper.extractCustomFieldIds(
                variant.customFields,
              );
              const customFields =
                await this.customFieldRepository.findByIds(customFieldIds);
              this.metadataHelper.validateCustomFieldInputs(
                variant.customFields,
                customFields,
              );
            }

            return variantWithoutImages;
          },
        ),
      );

      // Cập nhật metadata với variants đã xử lý
      product.metadata = product.metadata || {};
      product.metadata.variants = processedVariants;
    }
  }

  /**
   * Đảm bảo shipment config = 0 cho sản phẩm số
   */
  private ensureZeroShipmentConfig(product: UserProduct): void {
    product.shipmentConfig = {
      widthCm: 0,
      heightCm: 0,
      lengthCm: 0,
      weightGram: 0,
    };
    this.logger.log('Set shipment config to zero for DIGITAL product');
  }

  /**
   * Xử lý cập nhật advanced info cho sản phẩm số
   */
  private async processAdvancedInfoUpdate(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): Promise<void> {
    if (!updateDto.advancedInfo || !product.detail_id) {
      return;
    }

    try {
      this.logger.log(
        `Updating advanced info for DIGITAL product ${product.id}`,
      );

      // Tìm advanced info hiện tại
      const existingAdvancedInfo =
        await this.productAdvancedInfoRepository.findOne({
          where: { id: product.detail_id },
        });

      if (existingAdvancedInfo) {
        const digitalAdvancedInfo = updateDto.advancedInfo as any;

        // Cập nhật digital fulfillment flow nếu có
        if (
          digitalAdvancedInfo.deliveryMethod ||
          digitalAdvancedInfo.deliveryTiming ||
          digitalAdvancedInfo.outputType
        ) {
          existingAdvancedInfo.digitalFulfillmentFlow = {
            deliveryMethod:
              digitalAdvancedInfo.deliveryMethod ||
              existingAdvancedInfo.digitalFulfillmentFlow?.deliveryMethod,
            deliveryTiming:
              digitalAdvancedInfo.deliveryTiming ||
              existingAdvancedInfo.digitalFulfillmentFlow?.deliveryTiming,
            outputType:
              digitalAdvancedInfo.outputType ||
              existingAdvancedInfo.digitalFulfillmentFlow?.outputType,
          };
        }

        // Cập nhật digital output nếu có
        if (digitalAdvancedInfo.variantMetadata) {
          existingAdvancedInfo.digitalOutput =
            digitalAdvancedInfo.variantMetadata;
        }

        existingAdvancedInfo.updatedAt = Date.now();

        await this.productAdvancedInfoRepository.save(existingAdvancedInfo);
        this.logger.log(
          `Successfully updated advanced info for DIGITAL product ${product.id}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Error updating advanced info for DIGITAL product ${product.id}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_UPDATE_FAILED,
        `Lỗi khi cập nhật advanced info: ${error.message}`,
      );
    }
  }

  /**
   * Validate classifications cho sản phẩm số
   */
  async validateDigitalClassifications(classifications: any[]): Promise<void> {
    for (const classification of classifications) {
      // Validate price cho classification
      if (!classification.price || !classification.price.listPrice) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Classification phải có giá',
        );
      }

      // Validate quantity constraints
      if (
        classification.minQuantityPerPurchase !== undefined &&
        classification.minQuantityPerPurchase < 0
      ) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Số lượng tối thiểu không thể âm',
        );
      }

      if (
        classification.maxQuantityPerPurchase !== undefined &&
        classification.maxQuantityPerPurchase < 0
      ) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Số lượng tối đa không thể âm',
        );
      }

      if (
        classification.minQuantityPerPurchase &&
        classification.maxQuantityPerPurchase &&
        classification.minQuantityPerPurchase >
          classification.maxQuantityPerPurchase
      ) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Số lượng tối thiểu không thể lớn hơn số lượng tối đa',
        );
      }
    }
  }
}
