import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Logger, Param, ParseIntPipe, Post, Query, UseGuards, Patch } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiExtraModels, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { UserOrderService } from '../services/user-order.service';
import { JwtUserGuard } from '@modules/auth/guards';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import {
  QueryUserOrderDto,
  UserOrderListItemDto,
  UserOrderResponseDto,
  UserOrderStatusResponseDto,
  CreateUserOrderDto,
  UpdateUserOrderDto,
  TrackOrderResponseDto,
  CancelOrderRequestDto,
  CancelOrderResponseDto,
  ReturnOrderRequestDto,
  ReturnOrderResponseDto,
  DeliveryAgainOrderRequestDto,
  DeliveryAgainOrderResponseDto,
  PrintOrderByIdRequestDto,
  PrintOrderByIdResponseDto,
  TrackOrderQueryDto,
} from '../dto';
import { CurrentUser } from '@modules/auth/decorators';
import { ApiErrorResponse } from '@common/error/api-error-response.decorator';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { BulkDeleteUserOrderDto } from '../dto/bulk-delete-user-order.dto';
import { BulkDeleteUserOrderResponseDto } from '../dto/bulk-delete-user-order-response.dto';

/**
 * Controller xử lý các request liên quan đến đơn hàng của người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_ORDER)
@Controller('user/orders')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(
  ApiResponseDto,
  UserOrderResponseDto,
  UserOrderListItemDto,
  UserOrderStatusResponseDto,
  CreateUserOrderDto,
  UpdateUserOrderDto,
  TrackOrderResponseDto,
  CancelOrderResponseDto,
  ReturnOrderResponseDto,
  DeliveryAgainOrderResponseDto,
  PrintOrderByIdResponseDto,
  BulkDeleteUserOrderDto,
  BulkDeleteUserOrderResponseDto,
  PaginatedResult
)
export class UserOrderController {
  private readonly logger = new Logger(UserOrderController.name);

  constructor(private readonly userOrderService: UserOrderService) {}

  /**
   * Tạo đơn hàng mới
   * @param createOrderDto DTO chứa thông tin đơn hàng
   * @param user Thông tin người dùng từ JWT
   * @returns Thông tin đơn hàng đã tạo
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiBody({
    type: CreateUserOrderDto,
    examples: {
      'toi-thieu-ghn': {
        summary: '✅ Tối thiểu - GHN',
        description: 'Request ngắn gọn với các trường cần thiết sử dụng Giao Hàng Nhanh (GHN) - Người gửi trả phí vận chuyển (Freeship)',
        value: {
          shopId: 1,
          customerInfo: { customerId: 19 },
          products: [{ productId: 60, quantity: 2 }],
          billInfo: {
            subtotal: 300000,
            shippingFee: 31500,
            selectedCarrier: "GHN",
            shippingServiceType: "standard",
            discount: 10000,
            paymentMethod: "CASH"
          },
          logisticInfo: {
            shippingMethod: "Chuẩn",
            carrier: "GHN",
            shippingNote: "Giao hàng trong giờ hành chính",
            deliveryAddress: {
              addressId: 1
            }
          },
          // ❌ Xóa receiverPaysShipping vì giờ lấy từ User Provider Shipment setting
          source: "website",
          note: "Đơn hàng ưu tiên - GHN",
          tags: ["urgent", "ghn"]
        }
      },
      'toi-thieu-ghtk': {
        summary: '✅ Tối thiểu - GHTK',
        description: 'Request ngắn gọn với các trường cần thiết sử dụng Giao Hàng Tiết Kiệm (GHTK) - Người nhận trả phí vận chuyển',
        value: {
          shopId: 1,
          customerInfo: { customerId: 19 },
          products: [{ productId: 60, quantity: 2 }],
          billInfo: {
            subtotal: 300000,
            shippingFee: 21500,
            selectedCarrier: "GHTK",
            shippingServiceType: "standard",
            discount: 10000,
            paymentMethod: "CASH"
          },
          logisticInfo: {
            shippingMethod: "Đường bộ",
            carrier: "GHTK",
            shippingNote: "Giao hàng trong giờ hành chính",
            deliveryAddress: {
              addressId: 1
            }
          },
          // ❌ Xóa receiverPaysShipping vì giờ lấy từ User Provider Shipment setting
          source: "website",
          note: "Đơn hàng ưu tiên - GHTK",
          tags: ["urgent", "ghtk"]
        }
      }
    }
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
    BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
    BUSINESS_ERROR_CODES.CUSTOMER_NOT_FOUND
  )
  async createOrder(
    @Body() createOrderDto: CreateUserOrderDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<UserOrderResponseDto>> {
    try {
      this.logger.log(`Tạo đơn hàng mới cho userId=${user.id}`);
      const order = await this.userOrderService.createOrder(user.id, createOrderDto);
      return ApiResponseDto.success(order, 'Tạo đơn hàng thành công');
    } catch (error) {
      this.logger.error(`Lỗi khi tạo đơn hàng: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy danh sách đơn hàng của người dùng
   * @param queryDto DTO chứa các tham số truy vấn
   * @param user Thông tin người dùng từ JWT
   * @returns Danh sách đơn hàng với phân trang
   */
  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Lấy danh sách đơn hàng',
    description: 'Lấy danh sách đơn hàng của người dùng với thông tin khách hàng chuyển đổi đầy đủ (thay vì chỉ userConvertCustomerId)'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách đơn hàng với thông tin khách hàng đầy đủ',
    schema: ApiResponseDto.getPaginatedSchema(UserOrderListItemDto),
  })
  @ApiErrorResponse(BUSINESS_ERROR_CODES.ORDER_FIND_FAILED)
  async getOrders(
    @Query() queryDto: QueryUserOrderDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<PaginatedResult<UserOrderListItemDto>>> {
    try {
      this.logger.log(`Lấy danh sách đơn hàng cho userId=${user.id}`);
      const orders = await this.userOrderService.findAll(user.id, queryDto);
      return ApiResponseDto.success(orders, 'Lấy danh sách đơn hàng thành công');
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách đơn hàng: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy chi tiết đơn hàng theo ID
   * @param id ID của đơn hàng
   * @param user Thông tin người dùng từ JWT
   * @returns Chi tiết đơn hàng
   */
  @Get('detail/:id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Lấy chi tiết đơn hàng' })
  @ApiParam({ name: 'id', description: 'ID của đơn hàng', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Chi tiết đơn hàng',
    schema: ApiResponseDto.getSchema(UserOrderResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
    BUSINESS_ERROR_CODES.ORDER_ACCESS_DENIED,
    BUSINESS_ERROR_CODES.ORDER_FIND_FAILED
  )
  async getOrderDetail(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<UserOrderResponseDto>> {
    try {
      this.logger.log(`Lấy chi tiết đơn hàng id=${id} cho userId=${user.id}`);
      const order = await this.userOrderService.findById(id, user.id);
      return ApiResponseDto.success(order, 'Lấy chi tiết đơn hàng thành công');
    } catch (error) {
      this.logger.error(`Lỗi khi lấy chi tiết đơn hàng: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật đơn hàng
   * @param id ID của đơn hàng
   * @param updateOrderDto DTO chứa thông tin cập nhật
   * @param user Thông tin người dùng từ JWT
   * @returns Thông tin đơn hàng đã cập nhật
   */
  @Patch(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Cập nhật đơn hàng',
    description: 'Cập nhật thông tin đơn hàng (chỉ có thể cập nhật đơn hàng chưa hoàn thành hoặc chưa hủy)'
  })
  @ApiParam({ name: 'id', description: 'ID của đơn hàng', type: Number })
  @ApiBody({
    type: UpdateUserOrderDto,
    examples: {
      'cap-nhat-trang-thai': {
        summary: '✅ Cập nhật trạng thái đơn hàng',
        description: 'Cập nhật trạng thái đơn hàng và vận chuyển',
        value: {
          orderStatus: 'confirmed',
          shippingStatus: 'preparing'
        }
      },
      'cap-nhat-bill-info': {
        summary: '✅ Cập nhật thông tin hóa đơn',
        description: 'Cập nhật thông tin thanh toán và phí',
        value: {
          billInfo: {
            subtotal: 350000,
            shippingFee: 35000,
            discount: 15000,
            paymentMethod: 'BANK_TRANSFER',
            paymentStatus: 'PAID'
          }
        }
      },
      'cap-nhat-logistic': {
        summary: '✅ Cập nhật thông tin vận chuyển',
        description: 'Cập nhật địa chỉ giao hàng và ghi chú',
        value: {
          logisticInfo: {
            shippingMethod: 'Nhanh',
            carrier: 'GHN',
            shippingNote: 'Giao hàng trong giờ hành chính',
            deliveryAddress: {
              existingAddressId: 5
            }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cập nhật đơn hàng thành công',
    schema: ApiResponseDto.getSchema(UserOrderResponseDto)
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
    BUSINESS_ERROR_CODES.ORDER_ACCESS_DENIED,
    BUSINESS_ERROR_CODES.ORDER_UPDATE_FAILED
  )
  async updateOrder(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateOrderDto: UpdateUserOrderDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<UserOrderResponseDto>> {
    try {
      this.logger.log(`Cập nhật đơn hàng id=${id} cho userId=${user.id}`);
      const order = await this.userOrderService.updateOrder(id, user.id, updateOrderDto);
      return ApiResponseDto.success(order, 'Cập nhật đơn hàng thành công');
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật đơn hàng: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy thống kê trạng thái đơn hàng và vận chuyển của người dùng
   */
  @Get('status-stats')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Lấy thống kê trạng thái đơn hàng và vận chuyển',
    description: 'Lấy thống kê số lượng đơn hàng theo trạng thái đơn hàng và trạng thái vận chuyển của người dùng hiện tại',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy thống kê thành công',
    schema: {
      allOf: [
        { $ref: '#/components/schemas/ApiResponseDto' },
        {
          properties: {
            data: { $ref: '#/components/schemas/UserOrderStatusResponseDto' },
          },
        },
      ],
    },
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.ORDER_FIND_FAILED,
  )
  async getOrderStatusStats(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<UserOrderStatusResponseDto>> {
    try {
      this.logger.log(`Lấy thống kê trạng thái đơn hàng cho userId=${user.id}`);
      const stats = await this.userOrderService.getOrderStatusStats(user.id);
      return ApiResponseDto.success(stats, 'Lấy thống kê trạng thái đơn hàng thành công');
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thống kê trạng thái đơn hàng: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tracking đơn hàng theo ID
   * @param id ID của đơn hàng
   * @param queryDto Query parameters
   * @param user Thông tin người dùng từ JWT
   * @returns Thông tin tracking đơn hàng
   */
  @Get(':id/track')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Tracking đơn hàng theo ID',
    description: 'Tự động xác định đơn vị vận chuyển (GHN/GHTK) dựa trên thông tin đơn hàng và thực hiện tracking'
  })
  @ApiParam({ name: 'id', description: 'ID của đơn hàng', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tracking đơn hàng thành công',
    schema: ApiResponseDto.getSchema(TrackOrderResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
    BUSINESS_ERROR_CODES.ORDER_ACCESS_DENIED,
    BUSINESS_ERROR_CODES.ORDER_FIND_FAILED,
    BUSINESS_ERROR_CODES.ORDER_NO_SHIPPING_INFO
  )
  async trackOrder(
    @Param('id', ParseIntPipe) id: number,
    @Query() queryDto: TrackOrderQueryDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<TrackOrderResponseDto>> {
    try {
      this.logger.log(`Tracking đơn hàng id=${id} cho userId=${user.id}`);
      const trackingResult = await this.userOrderService.trackOrderById(id, user.id, queryDto.updateStatus);
      return ApiResponseDto.success(trackingResult, 'Tracking đơn hàng thành công');
    } catch (error) {
      this.logger.error(`Lỗi khi tracking đơn hàng: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Hủy đơn hàng theo ID
   * @param id ID của đơn hàng
   * @param cancelOrderDto DTO chứa thông tin hủy đơn
   * @param user Thông tin người dùng từ JWT
   * @returns Thông tin hủy đơn hàng
   */
  @Post(':id/cancel')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Hủy đơn hàng theo ID',
    description: 'Tự động xác định đơn vị vận chuyển (GHN/GHTK) dựa trên thông tin đơn hàng và thực hiện hủy đơn'
  })
  @ApiParam({ name: 'id', description: 'ID của đơn hàng', type: Number })
  @ApiBody({
    type: CancelOrderRequestDto,
    examples: {
      'huy-don-co-ly-do': {
        summary: '✅ Hủy đơn hàng có lý do',
        description: 'Hủy đơn hàng với lý do cụ thể',
        value: {
          orderId: 123,
          reason: 'Khách hàng yêu cầu hủy do thay đổi ý định'
        }
      },
      'huy-don-khong-ly-do': {
        summary: '✅ Hủy đơn hàng không lý do',
        description: 'Hủy đơn hàng mà không cần lý do cụ thể',
        value: {
          orderId: 123
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Hủy đơn hàng thành công',
    schema: ApiResponseDto.getSchema(CancelOrderResponseDto)
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
    BUSINESS_ERROR_CODES.ORDER_ACCESS_DENIED,
    BUSINESS_ERROR_CODES.ORDER_UPDATE_FAILED,
    BUSINESS_ERROR_CODES.ORDER_NO_SHIPPING_INFO
  )
  async cancelOrder(
    @Param('id', ParseIntPipe) id: number,
    @Body() cancelOrderDto: CancelOrderRequestDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<CancelOrderResponseDto>> {
    try {
      this.logger.log(`Hủy đơn hàng id=${id} cho userId=${user.id}`);
      const cancelResult = await this.userOrderService.cancelOrderById(id, user.id, cancelOrderDto.reason);
      return ApiResponseDto.success(cancelResult, 'Hủy đơn hàng thành công');
    } catch (error) {
      this.logger.error(`Lỗi khi hủy đơn hàng: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Hoàn đơn hàng theo ID
   * @param id ID của đơn hàng
   * @param returnOrderDto DTO chứa thông tin hoàn đơn
   * @param user Thông tin người dùng từ JWT
   * @returns Thông tin hoàn đơn hàng
   */
  @Post(':id/return')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Hoàn đơn hàng theo ID',
    description: 'Tự động xác định đơn vị vận chuyển (GHN/GHTK) dựa trên thông tin đơn hàng và thực hiện hoàn đơn'
  })
  @ApiParam({ name: 'id', description: 'ID của đơn hàng', type: Number })
  @ApiBody({
    type: ReturnOrderRequestDto,
    examples: {
      'hoan-don-co-ly-do': {
        summary: '✅ Hoàn đơn hàng có lý do',
        description: 'Hoàn đơn hàng với lý do cụ thể',
        value: {
          orderId: 123,
          reason: 'Hàng bị hỏng khi giao đến khách hàng'
        }
      },
      'hoan-don-khong-ly-do': {
        summary: '✅ Hoàn đơn hàng không lý do',
        description: 'Hoàn đơn hàng mà không cần lý do cụ thể',
        value: {
          orderId: 123
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Hoàn đơn hàng thành công',
    schema: ApiResponseDto.getSchema(ReturnOrderResponseDto)
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
    BUSINESS_ERROR_CODES.ORDER_ACCESS_DENIED,
    BUSINESS_ERROR_CODES.ORDER_UPDATE_FAILED,
    BUSINESS_ERROR_CODES.ORDER_NO_SHIPPING_INFO
  )
  async returnOrder(
    @Param('id', ParseIntPipe) id: number,
    @Body() returnOrderDto: ReturnOrderRequestDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<ReturnOrderResponseDto>> {
    try {
      this.logger.log(`Hoàn đơn hàng id=${id} cho userId=${user.id}`);
      const returnResult = await this.userOrderService.returnOrderById(id, user.id, returnOrderDto.reason);
      return ApiResponseDto.success(returnResult, 'Hoàn đơn hàng thành công');
    } catch (error) {
      this.logger.error(`Lỗi khi hoàn đơn hàng: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Giao lại đơn hàng theo ID (chỉ GHN)
   * @param id ID của đơn hàng
   * @param deliveryAgainDto DTO chứa thông tin giao lại
   * @param user Thông tin người dùng từ JWT
   * @returns Thông tin giao lại đơn hàng
   */
  @Post(':id/delivery-again')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Giao lại đơn hàng theo ID (chỉ GHN)',
    description: 'Yêu cầu giao lại đơn hàng bị chuyển sang trạng thái storage (chỉ áp dụng cho đơn hàng GHN)'
  })
  @ApiParam({ name: 'id', description: 'ID của đơn hàng', type: Number })
  @ApiBody({
    type: DeliveryAgainOrderRequestDto,
    examples: {
      'giao-lai-co-ghi-chu': {
        summary: '✅ Giao lại có ghi chú',
        description: 'Yêu cầu giao lại đơn hàng với ghi chú cụ thể',
        value: {
          orderId: 123,
          notes: 'Khách hàng yêu cầu giao lại vào buổi chiều từ 14h-17h'
        }
      },
      'giao-lai-khong-ghi-chu': {
        summary: '✅ Giao lại không ghi chú',
        description: 'Yêu cầu giao lại đơn hàng mà không có ghi chú đặc biệt',
        value: {
          orderId: 123
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Yêu cầu giao lại đơn hàng thành công',
    schema: ApiResponseDto.getSchema(DeliveryAgainOrderResponseDto)
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
    BUSINESS_ERROR_CODES.ORDER_ACCESS_DENIED,
    BUSINESS_ERROR_CODES.ORDER_UPDATE_FAILED,
    BUSINESS_ERROR_CODES.ORDER_NO_SHIPPING_INFO
  )
  async deliveryAgainOrder(
    @Param('id', ParseIntPipe) id: number,
    @Body() deliveryAgainDto: DeliveryAgainOrderRequestDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<DeliveryAgainOrderResponseDto>> {
    try {
      this.logger.log(`Giao lại đơn hàng id=${id} cho userId=${user.id}`);
      const deliveryAgainResult = await this.userOrderService.deliveryAgainOrderById(id, user.id, deliveryAgainDto.notes);
      return ApiResponseDto.success(deliveryAgainResult, 'Yêu cầu giao lại đơn hàng thành công');
    } catch (error) {
      this.logger.error(`Lỗi khi giao lại đơn hàng: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * In đơn hàng theo ID
   * @param id ID của đơn hàng
   * @param printOrderDto DTO chứa tùy chọn in
   * @param user Thông tin người dùng từ JWT
   * @returns Thông tin in đơn hàng hoặc file PDF
   */
  @Post(':id/print')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'In đơn hàng theo ID',
    description: 'Tự động xác định đơn vị vận chuyển (GHN/GHTK) và tạo nhãn in tương ứng. GHN trả về token, GHTK trả về file PDF'
  })
  @ApiParam({ name: 'id', description: 'ID của đơn hàng', type: Number })
  @ApiBody({
    type: PrintOrderByIdRequestDto,
    examples: {
      'in-ghn-a5': {
        summary: '✅ In đơn GHN khổ A5',
        description: 'In đơn hàng GHN với format A5 (mặc định)',
        value: {
          orderId: 123,
          ghnFormat: 'A5'
        }
      },
      'in-ghn-thermal': {
        summary: '✅ In đơn GHN thermal 80x80',
        description: 'In đơn hàng GHN với format thermal 80x80mm',
        value: {
          orderId: 123,
          ghnFormat: '80x80'
        }
      },
      'in-ghtk-a6': {
        summary: '✅ In đơn GHTK khổ A6',
        description: 'In đơn hàng GHTK với khổ giấy A6 hướng dọc',
        value: {
          orderId: 123,
          paperSize: 'A6',
          orientation: 'portrait'
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'In đơn hàng thành công',
    schema: ApiResponseDto.getSchema(PrintOrderByIdResponseDto)
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
    BUSINESS_ERROR_CODES.ORDER_ACCESS_DENIED,
    BUSINESS_ERROR_CODES.ORDER_PRINT_FAILED,
    BUSINESS_ERROR_CODES.ORDER_NO_SHIPPING_INFO
  )
  async printOrder(
    @Param('id', ParseIntPipe) id: number,
    @Body() printOrderDto: PrintOrderByIdRequestDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<PrintOrderByIdResponseDto>> {
    try {
      this.logger.log(`In đơn hàng id=${id} cho userId=${user.id}`);
      const printResult = await this.userOrderService.printOrderById(id, user.id, printOrderDto);

      // Nếu là GHTK và có pdfBuffer, cần xử lý đặc biệt
      if (printResult.printType === 'pdf' && printResult.pdfBuffer) {
        // Loại bỏ pdfBuffer khỏi response JSON (sẽ được handle riêng nếu cần)
        const { pdfBuffer, ...responseData } = printResult;
        return ApiResponseDto.success(responseData, 'In đơn hàng thành công');
      }

      return ApiResponseDto.success(printResult, 'In đơn hàng thành công');
    } catch (error) {
      this.logger.error(`Lỗi khi in đơn hàng: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xóa nhiều đơn hàng
   * @param bulkDeleteDto DTO chứa danh sách ID đơn hàng cần xóa
   * @param user Thông tin người dùng từ JWT
   * @returns Kết quả xóa nhiều đơn hàng
   */
  @Delete('bulk')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Xóa nhiều đơn hàng',
    description: 'Xóa nhiều đơn hàng cùng lúc theo danh sách ID'
  })
  @ApiBody({
    type: BulkDeleteUserOrderDto,
    examples: {
      'xoa-nhieu-don-hang': {
        summary: '✅ Xóa nhiều đơn hàng',
        description: 'Xóa nhiều đơn hàng theo danh sách ID',
        value: {
          orderIds: [1, 2, 3, 4, 5]
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa nhiều đơn hàng thành công',
    schema: ApiResponseDto.getSchema(BulkDeleteUserOrderResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.ORDER_BULK_DELETE_FAILED,
    BUSINESS_ERROR_CODES.ORDER_BULK_DELETE_VALIDATION_FAILED,
    BUSINESS_ERROR_CODES.ORDER_BULK_DELETE_PARTIAL_SUCCESS
  )
  async bulkDeleteOrders(
    @Body() bulkDeleteDto: BulkDeleteUserOrderDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<BulkDeleteUserOrderResponseDto>> {
    try {
      this.logger.log(`Xóa bulk ${bulkDeleteDto.orderIds.length} đơn hàng cho userId=${user.id}`);
      const result = await this.userOrderService.bulkDeleteOrders(user.id, bulkDeleteDto);
      return ApiResponseDto.success(result, result.message);
    } catch (error) {
      this.logger.error(`Lỗi khi xóa bulk đơn hàng: ${error.message}`, error.stack);
      throw error;
    }
  }

}
