/**
 * Types cho UserClassification entity
 */

/**
 * Interface cho metadata của user classification
 */
export interface UserClassificationMetadata {
  /**
   * Danh sách custom fields
   */
  customFields: ClassificationCustomField[];

  /**
   * Thông tin bổ sung khác
   */
  additionalInfo?: {
    /**
     * Ghi chú về phân loại
     */
    notes?: string;

    /**
     * Thứ tự hiển thị
     */
    displayOrder?: number;

    /**
     * Trạng thái hiển thị
     */
    isVisible?: boolean;

    /**
     * Ngày tạo
     */
    createdAt?: number;

    /**
     * Ngày cập nhật
     */
    updatedAt?: number;

    /**
     * Thông tin bổ sung khác
     */
    [key: string]: any;
  };
}

/**
 * Interface cho custom field trong classification
 */
export interface ClassificationCustomField {
  /**
   * ID của custom field
   */
  fieldId: number;

  /**
   * Giá trị của custom field
   */
  value: any;

  /**
   * Loại dữ liệu
   */
  dataType?: string;

  /**
   * Label hiển thị
   */
  label?: string;
}

/**
 * Interface cho images media của classification
 */
export interface ClassificationImagesMedia {
  /**
   * Danh sách hình ảnh
   */
  images: ClassificationImage[];

  /**
   * Hình ảnh chính (thumbnail)
   */
  thumbnail?: ClassificationImage;

  /**
   * Metadata về media
   */
  metadata?: {
    /**
     * Tổng số hình ảnh
     */
    totalImages?: number;

    /**
     * Kích thước tối đa cho phép
     */
    maxFileSize?: number;

    /**
     * Định dạng file được phép
     */
    allowedFormats?: string[];

    /**
     * Thông tin bổ sung
     */
    [key: string]: any;
  };
}

/**
 * Interface cho từng hình ảnh trong classification
 */
export interface ClassificationImage {
  /**
   * Key/path của hình ảnh
   */
  key: string;

  /**
   * Vị trí hiển thị
   */
  position: number;

  /**
   * URL của hình ảnh (có thể được generate từ CDN)
   */
  url?: string;

  /**
   * Alt text cho hình ảnh
   */
  alt?: string;

  /**
   * Kích thước file (bytes)
   */
  fileSize?: number;

  /**
   * Định dạng file
   */
  mimeType?: string;

  /**
   * Kích thước hình ảnh
   */
  dimensions?: {
    width: number;
    height: number;
  };

  /**
   * Thời gian upload
   */
  uploadedAt?: number;
}

/**
 * Interface cho custom fields của classification (field riêng biệt)
 */
export interface ClassificationCustomFields {
  /**
   * Danh sách các field tùy chỉnh
   */
  fields: ClassificationCustomFieldValue[];

  /**
   * Metadata về custom fields
   */
  metadata?: {
    /**
     * Phiên bản schema
     */
    schemaVersion?: string;

    /**
     * Thời gian cập nhật cuối
     */
    lastUpdated?: number;

    /**
     * Thông tin validation
     */
    validationRules?: any;

    /**
     * Thông tin bổ sung
     */
    [key: string]: any;
  };
}

/**
 * Interface cho giá trị của custom field
 */
export interface ClassificationCustomFieldValue {
  /**
   * ID của custom field definition
   */
  customFieldId: number;

  /**
   * Giá trị thực tế
   */
  value: {
    /**
     * Giá trị chính
     */
    value: any;

    /**
     * Metadata bổ sung cho giá trị
     */
    metadata?: {
      /**
       * Thời gian cập nhật
       */
      updatedAt?: number;

      /**
       * Người cập nhật
       */
      updatedBy?: number;

      /**
       * Ghi chú
       */
      notes?: string;

      /**
       * Thông tin bổ sung
       */
      [key: string]: any;
    };
  };

  /**
   * Thông tin về field definition (có thể cache)
   */
  fieldInfo?: {
    /**
     * Tên field
     */
    name?: string;

    /**
     * Loại dữ liệu
     */
    dataType?: string;

    /**
     * Label hiển thị
     */
    label?: string;

    /**
     * Mô tả
     */
    description?: string;
  };
}

/**
 * Utility functions để làm việc với UserClassification types
 */
export class UserClassificationUtils {
  /**
   * Tạo metadata mặc định cho classification
   */
  static createDefaultMetadata(): UserClassificationMetadata {
    return {
      customFields: [],
      additionalInfo: {
        isVisible: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
    };
  }

  /**
   * Tạo images media mặc định
   */
  static createDefaultImagesMedia(): ClassificationImagesMedia {
    return {
      images: [],
      metadata: {
        totalImages: 0,
        maxFileSize: 5 * 1024 * 1024, // 5MB
        allowedFormats: ['image/jpeg', 'image/png', 'image/webp'],
      },
    };
  }

  /**
   * Tạo custom fields mặc định
   */
  static createDefaultCustomFields(): ClassificationCustomFields {
    return {
      fields: [],
      metadata: {
        schemaVersion: '1.0',
        lastUpdated: Date.now(),
      },
    };
  }

  /**
   * Validate số lượng mua
   */
  static validatePurchaseQuantity(
    minQuantity: number | null,
    maxQuantity: number | null
  ): { isValid: boolean; error?: string } {
    if (minQuantity !== null && maxQuantity !== null) {
      if (minQuantity > maxQuantity) {
        return {
          isValid: false,
          error: 'Số lượng tối thiểu không được lớn hơn số lượng tối đa',
        };
      }
    }

    if (minQuantity !== null && minQuantity < 1) {
      return {
        isValid: false,
        error: 'Số lượng tối thiểu phải lớn hơn 0',
      };
    }

    if (maxQuantity !== null && maxQuantity < 1) {
      return {
        isValid: false,
        error: 'Số lượng tối đa phải lớn hơn 0',
      };
    }

    return { isValid: true };
  }

  /**
   * Thêm custom field vào metadata
   */
  static addCustomFieldToMetadata(
    metadata: UserClassificationMetadata,
    fieldId: number,
    value: any,
    dataType?: string,
    label?: string
  ): UserClassificationMetadata {
    const newField: ClassificationCustomField = {
      fieldId,
      value,
      dataType,
      label,
    };

    return {
      ...metadata,
      customFields: [...metadata.customFields, newField],
      additionalInfo: {
        ...metadata.additionalInfo,
        updatedAt: Date.now(),
      },
    };
  }

  /**
   * Thêm hình ảnh vào images media
   */
  static addImageToMedia(
    imagesMedia: ClassificationImagesMedia,
    image: Omit<ClassificationImage, 'position'>
  ): ClassificationImagesMedia {
    const position = imagesMedia.images.length;
    const newImage: ClassificationImage = {
      ...image,
      position,
      uploadedAt: Date.now(),
    };

    return {
      ...imagesMedia,
      images: [...imagesMedia.images, newImage],
      metadata: {
        ...imagesMedia.metadata,
        totalImages: imagesMedia.images.length + 1,
      },
    };
  }
}
