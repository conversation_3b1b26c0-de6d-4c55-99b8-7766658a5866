import { Injectable, Logger } from '@nestjs/common';
import {
  ProductAdvancedInfoRepository,
} from '@modules/business/repositories';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { UserProduct } from '@modules/business/entities';
import { ValidationHelper } from '../../../helpers/validation.helper';
import { ProductValidationHelper } from '../../../helpers/product-validation.helper';
import { ClassificationService } from '../../classification.service';
import { CreateProductProcessor } from './create-product.processor';
import { CreateProductResult } from './create-product-orchestrator';
import { S3Service } from '@shared/services/s3.service';
import { CategoryFolderEnum, generateS3Key } from '@shared/utils/generators/s3-key-generator.util';
import { FileSizeEnum, ImageTypeEnum, TimeIntervalEnum } from '@shared/utils';
import { DigitalProductCreateDto } from '../../../dto/request/create/digital-product-create.dto';
import { DigitalClassificationDto } from '../../../dto/digital-classification.dto';
import { CreateClassificationDto, ClassificationResponseDto } from '../../../dto/classification.dto';
import { PriceTypeEnum } from '@modules/business/enums';

/**
 * Processor chuyên xử lý logic tạo sản phẩm số
 * Bóc tách từ UserProductService để tối ưu hóa và dễ maintain
 */
@Injectable()
export class DigitalProductProcessor {
  private readonly logger = new Logger(DigitalProductProcessor.name);

  constructor(
    private readonly createProcessor: CreateProductProcessor,
    private readonly productAdvancedInfoRepository: ProductAdvancedInfoRepository,
    private readonly validationHelper: ValidationHelper,
    private readonly productValidationHelper: ProductValidationHelper,
    private readonly classificationService: ClassificationService,
    private readonly s3Service: S3Service,
  ) {}

  /**
   * Tạo sản phẩm số hoàn chỉnh
   */
  async createDigitalProduct(
    dto: DigitalProductCreateDto,
    userId: number,
  ): Promise<CreateProductResult> {
    this.logger.log(`Creating DIGITAL product: ${dto.name}`);

    // BƯỚC 1: Validate dữ liệu đầu vào cho sản phẩm số
    await this.validateDigitalProductData(dto);

    // BƯỚC 2: Xử lý classifications metadata và custom fields
    const classificationsMetadata = await this.processDigitalClassificationsMetadata(dto);
    const { customFields, metadata } = await this.createProcessor.processCustomFields(dto, classificationsMetadata);

    // BƯỚC 3: Tạo sản phẩm cơ bản với shipment config = 0
    const product = await this.createProcessor.createBaseProduct(dto, userId, metadata);
    product.shipmentConfig = { widthCm: 0, heightCm: 0, lengthCm: 0, weightGram: 0 };

    // BƯỚC 4: Lưu sản phẩm để có ID
    const savedProduct = await this.createProcessor.saveProduct(product);

    // BƯỚC 5: Xử lý hình ảnh sản phẩm chính
    const { imageEntries, imagesUploadUrls } = await this.createProcessor.processProductImages(dto, Date.now());

    // BƯỚC 6: Cập nhật sản phẩm với thông tin hình ảnh
    savedProduct.images = imageEntries;
    await this.createProcessor.saveProduct(savedProduct);

    // BƯỚC 7: Tạo advanced info (classifications)
    const advancedInfo = await this.createAdvancedInfo(savedProduct.id, dto.productType, dto, []);

    // BƯỚC 8: Xử lý hình ảnh cho classifications
    const advancedImagesUploadUrls = await this.createAdvancedImagesUploadUrls(dto, dto.productType, Date.now());

    // BƯỚC 9: Cập nhật detail_id để liên kết với advanced info
    if (advancedInfo?.id) {
      savedProduct.detail_id = advancedInfo.id;
      await this.createProcessor.saveProduct(savedProduct);

      // Cập nhật variant metadata với image keys nếu có hình ảnh variants
      if (advancedImagesUploadUrls.length > 0) {
        await this.updateVariantMetadataWithImageKeys(savedProduct, advancedImagesUploadUrls);
      }
    }

    // BƯỚC 10: Xử lý classifications
    const classifications = await this.processClassifications(savedProduct.id, dto.classifications || [], userId);

    // BƯỚC 11: Lấy sản phẩm cuối cùng
    const finalProduct = await this.createProcessor.getProductById(savedProduct.id);

    // BƯỚC 12: Tạo object chứa upload URLs cho cả sản phẩm chính và variants
    const uploadUrls: any = {
      productId: finalProduct.id.toString(),
      imagesUploadUrls: imagesUploadUrls || []
    };

    // Thêm upload URLs cho hình ảnh variants nếu có
    if (advancedImagesUploadUrls && advancedImagesUploadUrls.length > 0) {
      uploadUrls.advancedImagesUploadUrls = advancedImagesUploadUrls;
    }

    return {
      product: finalProduct,
      uploadUrls: (imagesUploadUrls.length > 0 || advancedImagesUploadUrls.length > 0) ? uploadUrls : null,
      additionalInfo: {
        advancedInfo,
        classifications
      }
    };
  }

  /**
   * Validate dữ liệu đầu vào cho sản phẩm số
   */
  private async validateDigitalProductData(dto: DigitalProductCreateDto): Promise<void> {
    // Kiểm tra classifications có tồn tại không (bắt buộc cho sản phẩm số)
    if (!dto.classifications || dto.classifications.length === 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Classifications are required for digital products',
      );
    }

    // Kiểm tra price có tồn tại không (bắt buộc cho sản phẩm số)
    if (!dto.price) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Price is required for digital products',
      );
    }

    // Validate giá sản phẩm theo business rules (digital products luôn có giá cố định)
    this.validationHelper.validateProductPrice(dto.price, PriceTypeEnum.HAS_PRICE, dto.productType);

    // Validate digital fulfillment flow
    if (!dto.digitalFulfillmentFlow) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Digital fulfillment flow is required for digital products',
      );
    }

    // Validate digital output
    if (!dto.digitalOutput) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Digital output is required for digital products',
      );
    }

    // Validate từng classification
    for (const classification of dto.classifications) {
      if (classification.minQuantityPerPurchase > classification.maxQuantityPerPurchase) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
          `Classification "${classification.name}": minimum quantity cannot be greater than maximum quantity`,
        );
      }
    }

    this.logger.log(`Validated digital product data for: ${dto.name}`);
  }

  /**
   * Xử lý classifications metadata cho sản phẩm số
   */
  private async processDigitalClassificationsMetadata(dto: DigitalProductCreateDto): Promise<any> {
    this.logger.log(`Processing classifications metadata for digital product: ${dto.name}`);

    // Xử lý classifications thành metadata format
    return {
      classifications: dto.classifications || [],
      digitalFulfillmentFlow: dto.digitalFulfillmentFlow,
      digitalOutput: dto.digitalOutput,
      purchaseCount: dto.purchaseCount || 0,
    };
  }

  /**
   * Tạo advanced info cho sản phẩm số
   */
  private async createAdvancedInfo(
    productId: number,
    productType: string,
    dto: DigitalProductCreateDto,
    additionalData: any[],
  ): Promise<any> {
    this.logger.log(`Creating advanced info for digital product: ${productId}`);

    // Tạo advanced info cho sản phẩm số với classifications
    return {
      id: Date.now(), // Temporary ID
      productId,
      productType,
      purchaseCount: dto.purchaseCount || 0,
      digitalFulfillmentFlow: dto.digitalFulfillmentFlow,
      digitalOutput: dto.digitalOutput,
      classifications: dto.classifications,
    };
  }

  /**
   * Tạo upload URLs cho hình ảnh variants
   */
  private async createAdvancedImagesUploadUrls(
    dto: DigitalProductCreateDto,
    productType: string,
    timestamp: number,
  ): Promise<any[]> {
    const uploadUrls: any[] = [];

    // Tạo presigned URLs cho hình ảnh classifications
    for (const classification of dto.classifications) {
      if (classification.imagesMediaTypes && classification.imagesMediaTypes.length > 0) {
        for (let i = 0; i < classification.imagesMediaTypes.length; i++) {
          const mediaType = classification.imagesMediaTypes[i];
          const fileName = `digital-classification-${classification.name}-image-${i}-${timestamp}`;

          // TODO: Implement actual S3 presigned URL generation
          uploadUrls.push({
            url: `https://presigned-url-example.com/${fileName}`,
            key: fileName,
            index: i,
            classificationName: classification.name
          });
        }
      }
    }

    this.logger.log(`Created ${uploadUrls.length} upload URLs for classification images`);
    return uploadUrls;
  }

  /**
   * Cập nhật variant metadata với image keys
   */
  private async updateVariantMetadataWithImageKeys(
    product: UserProduct,
    advancedImagesUploadUrls: any[],
  ): Promise<void> {
    // TODO: Implement logic từ service gốc
    // Cập nhật variant metadata với image keys nếu có hình ảnh variants
    
    this.logger.log(`Updating variant metadata with image keys for product: ${product.id}`);
  }

  /**
   * Xử lý classifications cho sản phẩm
   */
  private async processClassifications(
    productId: number,
    classificationsDto: DigitalClassificationDto[],
    userId: number,
  ): Promise<ClassificationResponseDto[]> {
    if (!classificationsDto || classificationsDto.length === 0) {
      return [];
    }

    this.logger.log(`Processing ${classificationsDto.length} classifications for digital product: ${productId}`);

    const createdClassifications: ClassificationResponseDto[] = [];

    // Tạo từng classification
    for (const digitalClassificationDto of classificationsDto) {
      try {
        // Mapping từ DigitalClassificationDto sang CreateClassificationDto
        const createClassificationDto = this.mapDigitalToCreateClassificationDto(digitalClassificationDto);

        // Tạo classification thông qua ClassificationService
        const createdClassification = await this.classificationService.create(
          productId,
          createClassificationDto,
          userId,
        );

        createdClassifications.push(createdClassification);
        this.logger.log(`Created classification: ${createdClassification.id} for product: ${productId}`);
      } catch (error) {
        this.logger.error(`Failed to create classification for product ${productId}: ${error.message}`, error.stack);
        throw error;
      }
    }

    this.logger.log(`Successfully created ${createdClassifications.length} classifications for digital product: ${productId}`);
    return createdClassifications;
  }

  /**
   * Mapping từ DigitalClassificationDto sang CreateClassificationDto
   */
  private mapDigitalToCreateClassificationDto(digitalDto: DigitalClassificationDto): CreateClassificationDto {
    return {
      type: digitalDto.name, // Sử dụng name làm type
      description: digitalDto.description,
      price: digitalDto.price,
      customFields: digitalDto.customFields || [],
      imagesMediaTypes: digitalDto.imagesMediaTypes || [],
      sku: digitalDto.sku,
      availableQuantity: digitalDto.availableQuantity,
      minQuantityPerPurchase: digitalDto.minQuantityPerPurchase,
      maxQuantityPerPurchase: digitalDto.maxQuantityPerPurchase,
    };
  }
}
