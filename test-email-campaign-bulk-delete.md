# Test Email Campaign Bulk Delete API

## <PERSON><PERSON> tả
Test API xóa nhiều email campaign v<PERSON><PERSON> các trườ<PERSON> hợp khác nhau.

## API Endpoint
```
DELETE /marketing/email-campaigns
Content-Type: application/json
Authorization: Bearer {JWT_TOKEN}
```

## Request Body
```json
{
  "ids": [1, 2, 3, 4, 5]
}
```

## Test Cases

### 1. Test Bulk Delete - All Success
```bash
# Tạo một số email campaign trước
POST /marketing/email-campaigns
{
  "title": "Campaign 1",
  "description": "Test campaign 1",
  "platform": "email",
  "subject": "Test Subject 1",
  "content": "<p>Test content 1</p>",
  "segmentId": 1,
  "scheduledAt": null
}

POST /marketing/email-campaigns
{
  "title": "Campaign 2", 
  "description": "Test campaign 2",
  "platform": "email",
  "subject": "Test Subject 2",
  "content": "<p>Test content 2</p>",
  "segmentId": 1,
  "scheduledAt": null
}

# <PERSON>óa nhiều campaign
DELETE /marketing/email-campaigns
{
  "ids": [1, 2]
}

# Expected Response: 200 OK
{
  "success": true,
  "message": "Đã xóa 2 email campaign thành công",
  "data": {
    "deletedCount": 2,
    "failedCount": 0,
    "deletedIds": [1, 2],
    "failedIds": [],
    "message": "Đã xóa 2 email campaign thành công"
  }
}
```

### 2. Test Bulk Delete - Some Failed (Campaign Not Found)
```bash
DELETE /marketing/email-campaigns
{
  "ids": [1, 999, 2]
}

# Expected Response: 207 Multi-Status
{
  "success": true,
  "message": "Đã xóa 2 email campaign thành công, 1 campaign không thể xóa",
  "data": {
    "deletedCount": 2,
    "failedCount": 1,
    "deletedIds": [1, 2],
    "failedIds": [999],
    "message": "Đã xóa 2 email campaign thành công, 1 campaign không thể xóa"
  }
}
```

### 3. Test Bulk Delete - Campaign đang gửi (SENDING)
```bash
# Tạo campaign với status SENDING
POST /marketing/email-campaigns
{
  "title": "Sending Campaign",
  "description": "Campaign đang gửi",
  "platform": "email",
  "subject": "Sending Subject",
  "content": "<p>Sending content</p>",
  "segmentId": 1,
  "scheduledAt": null
}

# Cập nhật status thành SENDING (có thể cần API riêng hoặc database update)

# Thử xóa campaign đang gửi
DELETE /marketing/email-campaigns
{
  "ids": [3]
}

# Expected Response: 207 Multi-Status
{
  "success": true,
  "message": "Đã xóa 0 email campaign thành công, 1 campaign không thể xóa",
  "data": {
    "deletedCount": 0,
    "failedCount": 1,
    "deletedIds": [],
    "failedIds": [3],
    "message": "Đã xóa 0 email campaign thành công, 1 campaign không thể xóa"
  }
}
```

### 4. Test Validation Errors
```bash
# Empty array
DELETE /marketing/email-campaigns
{
  "ids": []
}
# Expected: 400 Bad Request - "Phải có ít nhất một item để xóa"

# Invalid data type
DELETE /marketing/email-campaigns
{
  "ids": ["abc", "def"]
}
# Expected: 400 Bad Request - "ID phải là số"

# Duplicate IDs
DELETE /marketing/email-campaigns
{
  "ids": [1, 1, 2]
}
# Expected: 400 Bad Request - "Danh sách ID không được trùng lặp"

# Missing ids field
DELETE /marketing/email-campaigns
{
  "campaignIds": [1, 2]
}
# Expected: 400 Bad Request - "Danh sách ID không được để trống"
```

### 5. Test Authorization
```bash
# No token
DELETE /marketing/email-campaigns
{
  "ids": [1, 2]
}
# Expected: 401 Unauthorized

# Invalid token
DELETE /marketing/email-campaigns
Authorization: Bearer invalid_token
{
  "ids": [1, 2]
}
# Expected: 401 Unauthorized

# Token của user khác
DELETE /marketing/email-campaigns
Authorization: Bearer {OTHER_USER_TOKEN}
{
  "ids": [1, 2]
}
# Expected: 200 OK nhưng failedIds sẽ chứa tất cả IDs vì không tìm thấy campaign của user này
```

## Response Schema
```json
{
  "success": boolean,
  "message": string,
  "data": {
    "deletedCount": number,
    "failedCount": number, 
    "deletedIds": number[],
    "failedIds": number[],
    "message": string
  }
}
```

## Business Rules
1. **Campaign Status**: Không thể xóa campaign có status = "SENDING"
2. **User Scope**: Chỉ có thể xóa campaign của chính user đó
3. **Cascade Delete**: Khi xóa campaign, cũng xóa luôn campaign history liên quan
4. **Transactional**: Mỗi campaign được xóa trong transaction riêng, lỗi ở 1 campaign không ảnh hưởng đến các campaign khác
5. **Error Handling**: Lỗi được ghi log và campaign ID được thêm vào failedIds

## Notes
- API sử dụng HTTP DELETE method với body (không phải query params)
- Response luôn trả về 200 OK hoặc 207 Multi-Status, không bao giờ 404 cho individual items
- Validation được thực hiện ở DTO level với class-validator
- Service method sử dụng @Transactional() decorator để đảm bảo data consistency
