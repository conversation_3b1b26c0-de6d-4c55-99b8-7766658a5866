  import { ApiProperty, ApiExtraModels } from '@nestjs/swagger';
  import { Type } from 'class-transformer';
  import {
    IsArray,
    IsEnum,
    IsNumber,
    IsObject,
    IsOptional,
    IsString,
    MaxLength,
    ValidateNested,
    IsIn,
  } from 'class-validator';
  import { PriceTypeEnum, ProductTypeEnum } from '@modules/business/enums';
  import { HasPriceDto, StringPriceDto } from './price.dto';
  import { BusinessShipmentConfigDto } from './create-product.dto';
  import { UpdateClassificationDto, ClassificationPriceDto, ClassificationStringPriceDto } from './classification.dto';
  import { CustomFieldInputDto } from './custom-field-metadata.dto';
  import { ProductInventoryDto } from './product-inventory.dto';
  import { DigitalAdvancedInfoDto } from './advanced-info/digital-advanced-info.dto';
  import { EventAdvancedInfoDto } from './advanced-info/event-advanced-info.dto';
  import { ServiceAdvancedInfoDto } from './advanced-info/service-advanced-info.dto';
  import { ComboAdvancedInfoDto } from './advanced-info/combo-advanced-info.dto';
  import { IsValidAdvancedInfo } from '../validators/product-advanced-info.validator';

  /**
   * DTO cho thao tác xử lý hình ảnh trong API cập nhật sản phẩm
   */
  export class ImageOperationDto {
    @ApiProperty({
      description: 'Loại thao tác với hình ảnh',
      enum: ['ADD', 'DELETE'],
      example: 'ADD',
    })
    @IsString()
    @IsIn(['ADD', 'DELETE'], { message: 'Operation chỉ chấp nhận giá trị ADD hoặc DELETE' })
    operation: 'ADD' | 'DELETE';

    @ApiProperty({
      description: 'Key của hình ảnh cần xóa (bắt buộc khi operation = DELETE)',
      example: 'business/IMAGE/2025/06/1748943694523-5219c3e1-745b-479b-8691-f14db4f62a66',
      required: false,
    })
    @IsOptional()
    @IsString()
    key?: string;

    @ApiProperty({
      description: 'MIME type của hình ảnh cần thêm (bắt buộc khi operation = ADD)',
      enum: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
      example: 'image/jpeg',
      required: false,
    })
    @IsOptional()
    @IsString()
    @IsIn(['image/jpeg', 'image/png', 'image/gif', 'image/webp'], {
      message: 'MIME type nên là image/jpeg, image/png, image/gif, image/webp,.. theo enum có sẵn'
    })
    mimeType?: string;

    @ApiProperty({
      description: 'Vị trí của hình ảnh (tùy chọn, hệ thống sẽ tự động gán nếu không có)',
      example: 0,
      required: false,
    })
    @IsOptional()
    @IsNumber()
    position?: number;
  }

  /**
   * DTO cho việc cập nhật sản phẩm trong module business
   */
  @ApiExtraModels(DigitalAdvancedInfoDto, EventAdvancedInfoDto, ServiceAdvancedInfoDto, ComboAdvancedInfoDto, HasPriceDto, StringPriceDto, ClassificationPriceDto, ClassificationStringPriceDto, ImageOperationDto)
  export class BusinessUpdateProductDto {
    @ApiProperty({
      description: 'Tên sản phẩm',
      example: 'Áo thun nam cao cấp',
      maxLength: 255,
      required: false,
    })
    @IsOptional()
    @IsString()
    @MaxLength(255)
    name?: string;

    @ApiProperty({
      description: 'Loại sản phẩm',
      enum: ProductTypeEnum,
      example: ProductTypeEnum.PHYSICAL,
      required: false,
    })
    @IsOptional()
    @IsEnum(ProductTypeEnum)
    productType?: ProductTypeEnum;

    @ApiProperty({
      description: 'Giá sản phẩm',
      oneOf: [
        { $ref: '#/components/schemas/HasPriceDto' },
        { $ref: '#/components/schemas/StringPriceDto' },
        { type: 'null' }
      ],
      example: {
        listPrice: 200000,
        salePrice: 150000,
        currency: 'VND'
      },
      required: false,
    })
    @IsOptional()
    price?: HasPriceDto | StringPriceDto | null;

    @ApiProperty({
      description: 'Loại giá',
      enum: PriceTypeEnum,
      example: PriceTypeEnum.HAS_PRICE,
      required: false,
    })
    @IsOptional()
    @IsEnum(PriceTypeEnum)
    typePrice?: PriceTypeEnum;

    @ApiProperty({
      description: 'Mô tả sản phẩm',
      example: 'Áo thun nam chất liệu cotton 100%',
      required: false,
    })
    @IsOptional()
    @IsString()
    description?: string;

    @ApiProperty({
      description: 'Danh sách MIME types cho hình ảnh mới (tương tự API tạo sản phẩm) - Sử dụng khi muốn thay thế toàn bộ hình ảnh hiện tại',
      type: [String],
      example: ['image/jpeg', 'image/png'],
      required: false,
    })
    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    @IsIn(['image/jpeg', 'image/png', 'image/gif', 'image/webp'], {
      each: true,
      message: 'MIME type chỉ chấp nhận: image/jpeg, image/png, image/gif, image/webp'
    })
    imagesMediaTypes?: string[];

    @ApiProperty({
      description: 'Danh sách thao tác xử lý hình ảnh (thêm/xóa cụ thể) - Sử dụng khi muốn thao tác chi tiết với từng hình ảnh',
      type: [ImageOperationDto],
      example: [
        {
          operation: 'DELETE',
          key: 'business/IMAGE/2025/06/1748943694523-5219c3e1-745b-479b-8691-f14db4f62a66'
        },
        {
          operation: 'ADD',
          mimeType: 'image/jpeg'
        }
      ],
      required: false,
    })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => ImageOperationDto)
    imageOperations?: ImageOperationDto[];

    @ApiProperty({
      description: 'Danh sách thao tác ảnh (deprecated - sử dụng imageOperations thay thế)',
      type: 'array',
      items: {
        type: 'object',
        properties: {
          operation: { type: 'string', enum: ['ADD', 'DELETE'], example: 'ADD' },
          position: { type: 'number', example: 1, description: 'Vị trí ảnh cần xóa (cho DELETE)' },
          key: { type: 'string', example: 'uploads/user_products/2025/05/image.jpg', description: 'Khóa tệp (cho DELETE)' },
          mimeType: { type: 'string', example: 'image/png', description: 'Loại MIME (cho ADD)' }
        }
      },
      example: [
        {
          operation: 'ADD',
          mimeType: 'image/jpeg'
        },
        {
          operation: 'DELETE',
          position: 0
        }
      ],
      required: false,
    })
    @IsOptional()
    @IsArray()
    images?: Array<{
      operation: 'ADD' | 'DELETE';
      position?: number;
      key?: string;
      mimeType?: string;
    }>;

    @ApiProperty({
      description: 'Danh sách tag',
      type: [String],
      example: ['áo thun', 'nam', 'cotton', 'cao cấp'],
      required: false,
    })
    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    tags?: string[];

    @ApiProperty({
      description: 'Danh sách custom fields cho sản phẩm',
      type: [CustomFieldInputDto],
      required: false,
    })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => CustomFieldInputDto)
    customFields?: CustomFieldInputDto[];

    @ApiProperty({
      description: 'Cấu hình vận chuyển (Không bắt buộc cho EVENT products)',
      type: BusinessShipmentConfigDto,
      required: false,
    })
    @IsOptional()
    @IsObject()
    @ValidateNested()
    @Type(() => BusinessShipmentConfigDto)
    shipmentConfig?: BusinessShipmentConfigDto;

    @ApiProperty({
      description: 'Danh sách phân loại sản phẩm cần cập nhật hoặc thêm mới',
      type: [UpdateClassificationDto],
      required: false,
    })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => UpdateClassificationDto)
    classifications?: UpdateClassificationDto[];

    @ApiProperty({
      description: 'Danh sách ID phân loại sản phẩm cần xóa',
      example: [5, 8],
      required: false,
      type: [Number]
    })
    @IsOptional()
    @IsArray()
    @IsNumber({}, { each: true })
    classificationsToDelete?: number[];

    @ApiProperty({
      description: 'Danh sách thông tin tồn kho sản phẩm ở các kho khác nhau - Chỉ bắt buộc đối với sản phẩm PHYSICAL. Không bắt buộc cho DIGITAL, SERVICE, EVENT và COMBO. Có thể truyền inventoryId để sử dụng inventory có sẵn hoặc truyền đầy đủ thông tin để tạo/cập nhật inventory',
      type: [ProductInventoryDto],
      isArray: true,
      required: false,
      examples: {
        'Sử dụng inventory có sẵn': {
          value: [
            {
              inventoryId: 123
            }
          ]
        },
        'Tạo/cập nhật inventory mới': {
          value: [
            {
              warehouseId: 1,
              availableQuantity: 100,
              sku: 'SKU-001',
              barcode: '1234567890123'
            },
            {
              warehouseId: 2,
              availableQuantity: 50,
              sku: 'SKU-002',
              barcode: '1234567890124'
            }
          ]
        }
      }
    })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => ProductInventoryDto)
    inventory?: ProductInventoryDto[];

    @ApiProperty({
      description: 'Thông tin nâng cao cho sản phẩm (chỉ áp dụng cho DIGITAL, EVENT, SERVICE, COMBO)',
      oneOf: [
        { $ref: '#/components/schemas/DigitalAdvancedInfoDto' },
        { $ref: '#/components/schemas/EventAdvancedInfoDto' },
        { $ref: '#/components/schemas/ServiceAdvancedInfoDto' },
        { $ref: '#/components/schemas/ComboAdvancedInfoDto' }
      ],
      example: {
        // Ví dụ cập nhật DIGITAL với variant management, custom fields và image operations
        purchaseCount: 150,
        digitalFulfillmentFlow: {
          deliveryMethod: 'dashboard_download',
          deliveryTiming: 'immediate',
          deliveryDelayMinutes: 0,
          accessStatus: 'delivered'
        },
        digitalOutput: {
          outputType: 'online_course',
          accessLink: 'https://course.example.com/activate?token=updated123',
          loginInfo: {
            username: 'auto_generated',
            password: 'new_temp_password'
          },
          usageInstructions: 'Vui lòng đăng nhập bằng thông tin mới để truy cập khóa học'
        },
        variantMetadata: {
          variants: [
            {
              name: 'Basic',
              sku: 'BASIC-002',
              availableQuantity: 1,
              minQuantityPerPurchase: 1,
              maxQuantityPerPurchase: 1,
              price: {
                listPrice: 600000,
                salePrice: 500000,
                currency: 'VND'
              },
              images: [
                {
                  operation: 'DELETE',
                  position: 0
                },
                {
                  operation: 'ADD',
                  mimeType: 'image/jpeg'
                }
              ],
              description: 'Phiên bản cơ bản đã cập nhật - Học React cơ bản',
              customFields: [
                {
                  customFieldId: 15,
                  value: {
                    value: 'Khóa học React cơ bản'
                  }
                },
                {
                  customFieldId: 18,
                  value: {
                    value: '20 giờ học'
                  }
                },
                {
                  customFieldId: 20,
                  value: {
                    value: 'Có video hướng dẫn'
                  }
                }
              ]
            },
            {
              name: 'Premium',
              sku: 'PREMIUM-001',
              availableQuantity: 1,
              minQuantityPerPurchase: 1,
              maxQuantityPerPurchase: 3,
              price: {
                listPrice: 2500000,
                salePrice: 2200000,
                currency: 'VND'
              },
              images: [
                {
                  operation: 'DELETE',
                  position: 0
                },
                {
                  operation: 'ADD',
                  mimeType: 'image/jpeg'
                },
                {
                  operation: 'ADD',
                  mimeType: 'image/png'
                },
                {
                  operation: 'ADD',
                  mimeType: 'image/webp'
                }
              ],
              description: 'Phiên bản cao cấp mới - Học React + NextJS + TypeScript + 1-on-1 mentoring',
              customFields: [
                {
                  customFieldId: 15,
                  value: {
                    value: 'Khóa học React cao cấp'
                  }
                },
                {
                  customFieldId: 16,
                  value: {
                    value: 'Có chứng chỉ quốc tế'
                  }
                },
                {
                  customFieldId: 17,
                  value: {
                    value: '80 giờ học'
                  }
                },
                {
                  customFieldId: 19,
                  value: {
                    value: 'Có mentor 1-on-1'
                  }
                },
                {
                  customFieldId: 21,
                  value: {
                    value: 'Có dự án thực tế'
                  }
                },
                {
                  customFieldId: 22,
                  value: {
                    value: 'Hỗ trợ 24/7'
                  }
                }
              ]
            }
          ]
        }
      },
      required: false,
    })
    @IsOptional()
    @IsObject()
    @IsValidAdvancedInfo()
    advancedInfo?: DigitalAdvancedInfoDto | EventAdvancedInfoDto | ServiceAdvancedInfoDto | ComboAdvancedInfoDto;

    // Service-specific fields for frontend compatibility
    @ApiProperty({
      description: 'Thời gian thực hiện dịch vụ (timestamp) - Chỉ áp dụng cho sản phẩm SERVICE',
      example: 1704067200000,
      required: false,
    })
    @IsOptional()
    @IsNumber()
    @Type(() => Number)
    serviceTime?: number;

    @ApiProperty({
      description: 'Thời lượng dịch vụ (phút) - Chỉ áp dụng cho sản phẩm SERVICE',
      example: '60',
      required: false,
    })
    @IsOptional()
    @IsString()
    serviceDuration?: string;

    @ApiProperty({
      description: 'Nhà cung cấp dịch vụ - Chỉ áp dụng cho sản phẩm SERVICE',
      example: 'Công ty tư vấn ABC',
      required: false,
    })
    @IsOptional()
    @IsString()
    serviceProvider?: string;

    @ApiProperty({
      description: 'Loại dịch vụ - Chỉ áp dụng cho sản phẩm SERVICE',
      example: 'CONSULTATION',
      enum: ['CONSULTATION', 'TRAINING', 'SUPPORT', 'MAINTENANCE', 'OTHER'],
      required: false,
    })
    @IsOptional()
    @IsString()
    serviceType?: string;

    @ApiProperty({
      description: 'Địa điểm thực hiện dịch vụ - Chỉ áp dụng cho sản phẩm SERVICE',
      example: 'AT_CENTER',
      enum: ['AT_CENTER', 'AT_CUSTOMER', 'ONLINE', 'HYBRID'],
      required: false,
    })
    @IsOptional()
    @IsString()
    serviceLocation?: string;
  }
